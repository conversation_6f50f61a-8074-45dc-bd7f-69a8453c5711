#!/usr/bin/env python3
"""
K-12 Math Anxiety Intervention Study - Data Preparation
Phase 1: Data integration, quality check, and descriptive analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Set plotting parameters for APA-style publication
plt.style.use('seaborn-v0_8-whitegrid')

# Define APA-compatible color palette
# Using colorblind-friendly colors that work in grayscale
APA_COLORS = {
    'control': '#E69F00',      # Orange (prints well in grayscale)
    'treatment': '#56B4E9',    # Sky blue
    'accent1': '#009E73',      # Bluish green
    'accent2': '#F0E442',      # Yellow
    'accent3': '#0072B2',      # Blue
    'accent4': '#D55E00',      # Vermillion
    'accent5': '#CC79A7',      # Reddish purple
    'accent6': '#999999'       # Gray
}

# Set color palette
sns.set_palette([APA_COLORS['control'], APA_COLORS['treatment'], 
                 APA_COLORS['accent1'], APA_COLORS['accent3']])

# APA figure specifications
plt.rcParams['figure.figsize'] = (8, 6)  # APA max width is usually 7-8 inches
plt.rcParams['font.size'] = 10
plt.rcParams['axes.labelsize'] = 10
plt.rcParams['axes.titlesize'] = 11
plt.rcParams['xtick.labelsize'] = 9
plt.rcParams['ytick.labelsize'] = 9
plt.rcParams['legend.fontsize'] = 9
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'DejaVu Sans']
plt.rcParams['axes.spines.top'] = False
plt.rcParams['axes.spines.right'] = False

# ===== 1. DATA LOADING AND INTEGRATION =====

def load_and_merge_data(base_path="/Users/<USER>/math_anxiety/"):
    """Load main dataset and LIWC results"""
    
    # Load main dataset
    print("Loading main dataset...")
    df_main = pd.read_csv(base_path + "QLS_Full_Aug3.csv")
    
    # Load LIWC results
    print("Loading LIWC results...")
    liwc1 = pd.read_csv(base_path + "LIWC1_LIWC2015_results.csv")
    liwc2 = pd.read_csv(base_path + "LIWC2_LIWC2015_results.csv")
    
    # Rename LIWC columns to avoid conflicts
    liwc1_cols = {col: f'liwc1_{col}' if col != 'ID' else col for col in liwc1.columns}
    liwc2_cols = {col: f'liwc2_{col}' if col != 'ID' else col for col in liwc2.columns}
    
    liwc1.rename(columns=liwc1_cols, inplace=True)
    liwc2.rename(columns=liwc2_cols, inplace=True)
    
    # Merge datasets
    df = df_main.copy()
    df = pd.merge(df, liwc1, on='ID', how='left')
    df = pd.merge(df, liwc2, on='ID', how='left')
    
    print(f"Dataset shape: {df.shape}")
    return df

# ===== 2. DATA QUALITY CHECK =====

def check_data_quality(df):
    """Comprehensive data quality assessment"""
    
    print("\n=== DATA QUALITY REPORT ===")
    if 'Group' in df.columns:
        print("\n1. Sample Size by Group:")
        print(df['Group'].value_counts())
        print(f"Total N = {len(df)}")
    else:
        print("\n⚠ No 'Group' column found in dataset.")
        print(f"Total N = {len(df)}")


    # Sample size by group
    print("\n1. Sample Size by Group:")
    print(df['Group'].value_counts())
    print(f"Total N = {len(df)}")
    
    # Grade distribution
    print("\n2. Grade Distribution:")
    print(df['Grades'].value_counts().sort_index())
    
    # Class type distribution
    print("\n3. Class Type Distribution:")
    class_counts = df['Class_Type'].value_counts()
    fast_track = class_counts[class_counts.index.isin(['Pearl', 'Amber'])].sum()
    regular = class_counts[~class_counts.index.isin(['Pearl', 'Amber'])].sum()
    print(f"Fast-track classes (Pearl/Amber): {fast_track}")
    print(f"Regular classes: {regular}")
    
    # Missing data patterns
    print("\n4. Missing Data Analysis:")
    key_vars = ['MASR_Mean', 'Working_Memory_Score', 'ZFirst_Score', 'ZSecond_Score']
    
    for var in key_vars:
        missing_pct = df[var].isna().sum() / len(df) * 100
        print(f"{var}: {missing_pct:.1f}% missing")
    
    # Complete cases
    print(f"\nComplete cases (baseline + scores): {df['complete_case'].sum()}")
    print(f"Has writing sample (1st): {(~df['@1_English_Writing'].isna()).sum()}")
    print(f"Has writing sample (2nd): {(~df['@2_English_Writing'].isna()).sum()}")
    
    return df

# ===== 3. VARIABLE CREATION =====

def create_analysis_variables(df):
    """Create additional variables for analysis"""
    
    # Create fast-track indicator
    df['fast_track'] = df['Class_Type'].isin(['Pearl', 'Amber']).astype(int)
    
    # Create grade dummies
    df['grade_6'] = (df['Grades'] == 6).astype(int)
    df['grade_7'] = (df['Grades'] == 7).astype(int)
    df['grade_8'] = (df['Grades'] == 8).astype(int)
    
    # Create anxiety categories based on quartiles
    df['high_anxiety'] = (df['MASR_Quartile'] == 4).astype(int)
    df['low_anxiety'] = (df['MASR_Quartile'] == 1).astype(int)
        
    # Ensure Score_Change is numeric
    df['Score_Change'] = pd.to_numeric(df['Score_Change'], errors='coerce')

    # Check if student improved
    df['improved'] = (df['Score_Change'] > 0).astype(int)
    df['improved_half_sd'] = (df['Score_Change'] > 0.5).astype(int)

    
    # Count available data points
    df['n_scores'] = 2 - df[['ZFirst_Score', 'ZSecond_Score']].isna().sum(axis=1)
    df['n_writings'] = 2 - df[['@1_English_Writing', '@2_English_Writing']].isna().sum(axis=1)
    
    return df

# ===== 4. DESCRIPTIVE STATISTICS =====

def generate_descriptive_stats(df):
    """Generate comprehensive descriptive statistics"""
    # Clean key variables to ensure numeric type
    baseline_vars = ['MASR_Mean', 'ExamAnxiety_Mean', 'Working_Memory_Score']
    outcome_vars = ['ZFirst_Score', 'ZSecond_Score', 'Score_Change']
    all_vars = baseline_vars + outcome_vars

    for var in all_vars:
        df[var] = pd.to_numeric(df[var], errors='coerce')  # invalid strings → NaN

    # Select numeric variables
    baseline_vars = ['MASR_Mean', 'ExamAnxiety_Mean', 'Working_Memory_Score']
    outcome_vars = ['ZFirst_Score', 'ZSecond_Score', 'Score_Change']
    
    # Overall descriptives
    print("\n=== DESCRIPTIVE STATISTICS ===")
    print("\n1. Baseline Measures:")
    print(df[baseline_vars].describe())
    
    print("\n2. Outcome Measures:")
    print(df[outcome_vars].describe())
    
    # By group comparisons
    print("\n3. By Group Comparisons:")
    for var in baseline_vars + outcome_vars:
        if var in df.columns:
            control = df[df['Group'] == 0][var].dropna()
            treatment = df[df['Group'] == 1][var].dropna()
            
            if len(control) > 0 and len(treatment) > 0:
                # Test normality
                _, p_ctrl = stats.shapiro(control) if len(control) > 3 else (np.nan, np.nan)
                _, p_treat = stats.shapiro(treatment) if len(treatment) > 3 else (np.nan, np.nan)
                
                # Choose appropriate test
                if p_ctrl < 0.05 or p_treat < 0.05:
                    stat, p_val = stats.mannwhitneyu(control, treatment)
                    test_name = "Mann-Whitney U"
                else:
                    stat, p_val = stats.ttest_ind(control, treatment)
                    test_name = "t-test"
                
                print(f"\n{var}:")
                print(f"  Control: M={control.mean():.2f}, SD={control.std():.2f}")
                print(f"  Treatment: M={treatment.mean():.2f}, SD={treatment.std():.2f}")
                print(f"  {test_name}: p={p_val:.3f}")

# ===== 5. MISSING DATA ANALYSIS =====

def analyze_missing_patterns(df):
    """Analyze missing data patterns"""
    
    print("\n=== MISSING DATA PATTERNS ===")
    
    # Check if missingness is related to group assignment
    print("\n1. Missingness by Group:")
    for group in [0, 1]:
        group_df = df[df['Group'] == group]
        print(f"\nGroup {group}:")
        print(f"  Complete cases: {group_df['complete_case'].sum()} / {len(group_df)}")
        print(f"  Has 1st writing: {(~group_df['@1_English_Writing'].isna()).sum()} / {len(group_df)}")
        print(f"  Has 2nd writing: {(~group_df['@2_English_Writing'].isna()).sum()} / {len(group_df)}")
    
    # Check if baseline characteristics predict missingness
    print("\n2. Baseline Predictors of Missingness:")
    
    # Create missingness indicators
    df['missing_second_score'] = df['ZSecond_Score'].isna().astype(int)
    df['missing_writing'] = df['@1_English_Writing'].isna().astype(int)
    
    # Logistic regression would go here, but using simple comparisons for now
    for outcome in ['missing_second_score', 'missing_writing']:
        if df[outcome].sum() > 0:  # Only if there's missingness
            print(f"\n{outcome}:")
            for predictor in ['MASR_Mean', 'Group', 'fast_track']:
                complete = df[df[outcome] == 0][predictor].dropna()
                missing = df[df[outcome] == 1][predictor].dropna()
                
                if len(complete) > 0 and len(missing) > 0:
                    if predictor in ['Group', 'fast_track']:
                        # Chi-square for categorical
                        crosstab = pd.crosstab(df[predictor], df[outcome])
                        chi2, p_val = stats.chi2_contingency(crosstab)[:2]
                        print(f"  {predictor}: χ²={chi2:.2f}, p={p_val:.3f}")
                    else:
                        # t-test for continuous
                        t_stat, p_val = stats.ttest_ind(complete, missing)
                        print(f"  {predictor}: t={t_stat:.2f}, p={p_val:.3f}")

# ===== 6. DATA VISUALIZATION =====

def create_quality_plots(df):
    """Create APA-style visualizations for data quality"""
    
    # Use APA-compliant figure size
    fig, axes = plt.subplots(2, 2, figsize=(8, 8))
    
    # 1. Sample distribution
    ax = axes[0, 0]
    group_grade = pd.crosstab(df['Grades'], df['Group'])
    group_grade.plot(kind='bar', ax=ax, 
                    color=[APA_COLORS['control'], APA_COLORS['treatment']],
                    edgecolor='black', linewidth=0.5)
    ax.set_title('Sample Distribution by Grade and Group', fontweight='bold', pad=10)
    ax.set_xlabel('Grade')
    ax.set_ylabel('Frequency')
    ax.legend(['Control', 'Treatment'], frameon=False)
    ax.set_xticklabels(ax.get_xticklabels(), rotation=0)
    
    # 2. Missing data patterns
    ax = axes[0, 1]
    missing_vars = ['MASR_Mean', 'Working_Memory_Score', 'ZFirst_Score', 
                    'ZSecond_Score', '@1_English_Writing', '@2_English_Writing']
    missing_pct = [(df[var].isna().sum() / len(df) * 100) for var in missing_vars]
    
    bars = ax.bar(range(len(missing_vars)), missing_pct, 
                   color=APA_COLORS['accent4'], edgecolor='black', linewidth=0.5)
    ax.set_xticks(range(len(missing_vars)))
    ax.set_xticklabels(['MASR', 'WM', 'Score 1', 'Score 2', 'Writing 1', 'Writing 2'], 
                       rotation=45, ha='right')
    ax.set_ylabel('Missing Data (%)')
    ax.set_title('Missing Data by Variable', fontweight='bold', pad=10)
    ax.axhline(y=10, color=APA_COLORS['accent6'], linestyle='--', alpha=0.7, linewidth=1)
    
    # Add value labels on bars
    for i, (bar, pct) in enumerate(zip(bars, missing_pct)):
        if pct > 0:
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                   f'{pct:.0f}', ha='center', va='bottom', fontsize=8)
    
    # 3. Score distributions
    ax = axes[1, 0]
    
    # Create overlapping histograms with transparency
    bins = np.linspace(-3, 3, 25)
    ax.hist(df['ZFirst_Score'].dropna(), bins=bins, alpha=0.6, 
            color=APA_COLORS['control'], edgecolor='black', linewidth=0.5, label='Exam 1')
    ax.hist(df['ZSecond_Score'].dropna(), bins=bins, alpha=0.6, 
            color=APA_COLORS['treatment'], edgecolor='black', linewidth=0.5, label='Exam 2')
    
    ax.set_xlabel('Standardized Score (Z-score)')
    ax.set_ylabel('Frequency')
    ax.set_title('Distribution of Exam Scores', fontweight='bold', pad=10)
    ax.legend(frameon=False)
    ax.axvline(x=0, color=APA_COLORS['accent6'], linestyle='--', alpha=0.7, linewidth=1)
    
    # 4. Score change by group
    ax = axes[1, 1]
    
    # Prepare data for violin plot (more informative than boxplot)
    score_change_df = df[['Group', 'Score_Change']].dropna()
    
    # Create violin plot with box plot overlay
    parts = ax.violinplot([score_change_df[score_change_df['Group'] == 0]['Score_Change'],
                          score_change_df[score_change_df['Group'] == 1]['Score_Change']], 
                         positions=[1, 2], widths=0.6, showmeans=True, showextrema=True)
    
    # Color the violin plots
    colors = [APA_COLORS['control'], APA_COLORS['treatment']]
    for pc, color in zip(parts['bodies'], colors):
        pc.set_facecolor(color)
        pc.set_alpha(0.6)
        pc.set_edgecolor('black')
        pc.set_linewidth(0.5)
    
    # Style the other elements
    for partname in ('cbars', 'cmins', 'cmaxes', 'cmeans'):
        if partname in parts:
            parts[partname].set_edgecolor('black')
            parts[partname].set_linewidth(1)
    
    ax.set_xticks([1, 2])
    ax.set_xticklabels(['Control', 'Treatment'])
    ax.axhline(y=0, color=APA_COLORS['accent6'], linestyle='--', alpha=0.7, linewidth=1)
    ax.set_ylabel('Score Change (Z-score)')
    ax.set_title('Score Change by Group', fontweight='bold', pad=10)
    
    # Add sample sizes
    for i, group in enumerate([0, 1]):
        n = len(score_change_df[score_change_df['Group'] == group])
        ax.text(i+1, ax.get_ylim()[0] + 0.1, f'n = {n}', 
               ha='center', va='bottom', fontsize=8)
    
    # Overall figure adjustments
    plt.tight_layout()
    
    # Save in high resolution for publication
    plt.savefig('figure1_data_quality.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure1_data_quality.pdf', dpi=300, bbox_inches='tight')  # PDF for submission
    plt.show()

# ===== MAIN EXECUTION =====

def main():
    """Main execution function"""
    
    # Load data
    df = load_and_merge_data()
    
    # Quality check
    df = check_data_quality(df)
    
    # Create analysis variables
    df = create_analysis_variables(df)
    
    # Generate descriptive statistics
    generate_descriptive_stats(df)
    
    # Analyze missing patterns
    analyze_missing_patterns(df)
    
    # Create visualizations
    create_quality_plots(df)
    
    # Save processed dataset
    df.to_csv('k12_math_anxiety_processed.csv', index=False)
    print("\n✓ Processed dataset saved to 'k12_math_anxiety_processed.csv'")
    
    return df

if __name__ == "__main__":
    df = main()

#!/usr/bin/env python3
"""
K-12 Math Anxiety Intervention Study - LIWC Text Analysis
Phase 3: Deep analysis of linguistic features and patterns
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

# APA-style plotting parameters
plt.style.use('seaborn-v0_8-whitegrid')
APA_COLORS = {
    'control': '#E69F00',
    'treatment': '#56B4E9',
    'high_anx': '#D55E00',
    'low_anx': '#009E73',
    'improved': '#0072B2',
    'not_improved': '#CC79A7',
    'neutral': '#999999'
}

plt.rcParams.update({
    'figure.figsize': (8, 6),
    'font.size': 10,
    'axes.titlesize': 11,
    'axes.labelsize': 10,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 9,
    'font.family': 'sans-serif',
    'axes.spines.top': False,
    'axes.spines.right': False
})

# ===== 1. LIWC FEATURE ENGINEERING =====

def create_liwc_composites(df):
    """Create composite LIWC indices"""
    
    print("\n=== CREATING LIWC COMPOSITE INDICES ===")
    
    # Define LIWC variable groups
    liwc_vars = {
        'emotion': ['liwc1_posemo', 'liwc1_negemo', 'liwc1_anx', 'liwc1_anger', 'liwc1_sad'],
        'cognitive': ['liwc1_cogproc', 'liwc1_insight', 'liwc1_cause', 'liwc1_discrep', 'liwc1_tentat', 'liwc1_certain'],
        'temporal': ['liwc1_focuspast', 'liwc1_focuspresent', 'liwc1_focusfuture'],
        'pronouns': ['liwc1_i', 'liwc1_we', 'liwc1_you', 'liwc1_shehe', 'liwc1_they'],
        'drives': ['liwc1_achieve', 'liwc1_power', 'liwc1_reward', 'liwc1_risk']
    }
    
    # Create composite scores for first writing
    df['emotion_expression'] = df['liwc1_negemo'] + df['liwc1_anx']
    df['cognitive_processing'] = df['liwc1_insight'] + df['liwc1_cause'] + df['liwc1_discrep']
    df['self_focus'] = df['liwc1_i'] / (df['liwc1_ppron'] + 0.001)  # Avoid division by zero
    df['temporal_orientation'] = df['liwc1_focusfuture'] - df['liwc1_focuspast']
    df['certainty_ratio'] = df['liwc1_certain'] / (df['liwc1_tentat'] + 0.001)
    
    # For those with second writing, calculate change scores
    has_both = ~df['liwc2_WC'].isna()
    
    if has_both.sum() > 0:
        df.loc[has_both, 'emotion_change'] = (df.loc[has_both, 'liwc2_negemo'] + df.loc[has_both, 'liwc2_anx']) - \
                                             (df.loc[has_both, 'liwc1_negemo'] + df.loc[has_both, 'liwc1_anx'])
        df.loc[has_both, 'cognitive_change'] = (df.loc[has_both, 'liwc2_insight'] + df.loc[has_both, 'liwc2_cause']) - \
                                               (df.loc[has_both, 'liwc1_insight'] + df.loc[has_both, 'liwc1_cause'])
        df.loc[has_both, 'self_focus_change'] = (df.loc[has_both, 'liwc2_i'] / (df.loc[has_both, 'liwc2_ppron'] + 0.001)) - \
                                                (df.loc[has_both, 'liwc1_i'] / (df.loc[has_both, 'liwc1_ppron'] + 0.001))
    
    print(f"Created composite indices for {len(df)} participants")
    print(f"Participants with both writings: {has_both.sum()}")
    
    return df

# ===== 2. LIWC GROUP COMPARISONS =====

def compare_liwc_by_groups(df):
    """Compare LIWC features across different groups"""
    
    print("\n=== LIWC FEATURE COMPARISONS ===")
    
    # Key LIWC variables to analyze
    key_liwc = ['liwc1_WC', 'liwc1_anx', 'liwc1_posemo', 'liwc1_negemo', 
                'liwc1_cogproc', 'liwc1_insight', 'liwc1_cause', 'liwc1_i',
                'liwc1_focuspresent', 'liwc1_focusfuture']
    
    # 1. Treatment vs Control
    print("\n1. Treatment vs Control:")
    results_treatment = []
    
    for var in key_liwc:
        if var in df.columns:
            control = df[(df['Group'] == 0) & (~df[var].isna())][var]
            treatment = df[(df['Group'] == 1) & (~df[var].isna())][var]
            
            if len(control) > 3 and len(treatment) > 3:
                # Mann-Whitney U test (robust to non-normality)
                stat, p = stats.mannwhitneyu(control, treatment)
                
                # Calculate effect size (r = Z / sqrt(N))
                n_total = len(control) + len(treatment)
                z_score = stats.norm.ppf(stat / (len(control) * len(treatment)))
                effect_size = abs(z_score) / np.sqrt(n_total)
                
                results_treatment.append({
                    'Variable': var.replace('liwc1_', ''),
                    'Control_M': control.mean(),
                    'Control_SD': control.std(),
                    'Treatment_M': treatment.mean(),
                    'Treatment_SD': treatment.std(),
                    'p_value': p,
                    'effect_size_r': effect_size
                })
    
    treatment_df = pd.DataFrame(results_treatment)
    treatment_df['sig'] = treatment_df['p_value'].apply(lambda p: '***' if p < 0.001 else '**' if p < 0.01 else '*' if p < 0.05 else '')
    print(treatment_df.to_string(index=False))
    
    # 2. High vs Low Anxiety
    print("\n2. High vs Low Math Anxiety (Quartiles):")
    results_anxiety = []
    
    for var in key_liwc:
        if var in df.columns:
            low_anx = df[(df['MASR_Quartile'] == 1) & (~df[var].isna())][var]
            high_anx = df[(df['MASR_Quartile'] == 4) & (~df[var].isna())][var]
            
            if len(low_anx) > 3 and len(high_anx) > 3:
                stat, p = stats.mannwhitneyu(low_anx, high_anx)
                
                n_total = len(low_anx) + len(high_anx)
                z_score = stats.norm.ppf(stat / (len(low_anx) * len(high_anx)))
                effect_size = abs(z_score) / np.sqrt(n_total)
                
                results_anxiety.append({
                    'Variable': var.replace('liwc1_', ''),
                    'Low_Anx_M': low_anx.mean(),
                    'High_Anx_M': high_anx.mean(),
                    'p_value': p,
                    'effect_size_r': effect_size
                })
    
    anxiety_df = pd.DataFrame(results_anxiety)
    return treatment_df, anxiety_df

# ===== 3. LIWC PATTERN ANALYSIS =====

def analyze_liwc_patterns(df):
    """Identify writing patterns through clustering"""
    
    print("\n=== LIWC PATTERN ANALYSIS ===")
    
    # Select LIWC features for clustering
    liwc_features = ['liwc1_anx', 'liwc1_posemo', 'liwc1_negemo', 'liwc1_cogproc',
                     'liwc1_insight', 'liwc1_cause', 'liwc1_i', 'liwc1_focuspresent',
                     'liwc1_focusfuture', 'liwc1_certain', 'liwc1_tentat']
    
    # Prepare data
    df_cluster = df[liwc_features + ['ID', 'Group', 'MASR_Mean', 'Score_Change']].dropna()
    X = df_cluster[liwc_features].values
    
    # Standardize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # PCA for visualization
    pca = PCA(n_components=2)
    X_pca = pca.fit_transform(X_scaled)
    
    # K-means clustering
    kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
    clusters = kmeans.fit_predict(X_scaled)
    
    df_cluster['cluster'] = clusters
    df_cluster['pca1'] = X_pca[:, 0]
    df_cluster['pca2'] = X_pca[:, 1]
    
    # Analyze clusters
    print(f"\nPCA explained variance: {pca.explained_variance_ratio_}")
    print("\nCluster characteristics:")
    
    for i in range(3):
        cluster_data = df_cluster[df_cluster['cluster'] == i]
        print(f"\nCluster {i} (n={len(cluster_data)}):")
        print(f"  Score Change: M={cluster_data['Score_Change'].mean():.3f}")
        print(f"  MASR: M={cluster_data['MASR_Mean'].mean():.3f}")
        print(f"  Treatment %: {(cluster_data['Group'] == 1).mean()*100:.1f}%")
    
    return df_cluster

# ===== 4. PREDICTIVE ANALYSIS =====

def predict_improvement_from_liwc(df):
    """Use LIWC features to predict improvement"""
    
    print("\n=== PREDICTIVE ANALYSIS ===")
    
    # Define features and outcome
    liwc_predictors = ['liwc1_anx', 'liwc1_posemo', 'liwc1_negemo', 'liwc1_cogproc',
                       'liwc1_insight', 'liwc1_cause', 'liwc1_i', 'liwc1_we',
                       'liwc1_focuspresent', 'liwc1_focusfuture', 'liwc1_certain',
                       'liwc1_tentat', 'liwc1_achieve']
    
    # Add baseline characteristics
    baseline_predictors = ['MASR_Mean', 'Working_Memory_Score', 'Group']
    
    all_predictors = [p for p in liwc_predictors + baseline_predictors if p in df.columns]
    
    # Prepare data
    df_pred = df[all_predictors + ['Score_Change', 'improved_half_sd']].dropna()
    
    if len(df_pred) < 30:
        print("Insufficient data for predictive modeling")
        return None
    
    X = df_pred[all_predictors]
    y_continuous = df_pred['Score_Change']
    y_binary = df_pred['improved_half_sd']
    
    # Random Forest for continuous outcome
    rf = RandomForestRegressor(n_estimators=100, random_state=42, max_depth=5)
    scores = cross_val_score(rf, X, y_continuous, cv=5, scoring='r2')
    
    print(f"\nPredicting Score Change (R²): {scores.mean():.3f} (±{scores.std():.3f})")
    
    # Fit model to get feature importance
    rf.fit(X, y_continuous)
    feature_importance = pd.DataFrame({
        'feature': all_predictors,
        'importance': rf.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("\nTop 10 Important Features:")
    print(feature_importance.head(10).to_string(index=False))
    
    return feature_importance

# ===== 5. VISUALIZATION =====

def create_liwc_visualizations(df, df_cluster=None):
    """Create publication-quality LIWC visualizations"""
    
    fig, axes = plt.subplots(2, 2, figsize=(10, 10))
    
    # 1. Key LIWC features by group
    ax = axes[0, 0]
    features = ['liwc1_anx', 'liwc1_negemo', 'liwc1_insight', 'liwc1_i']
    feature_names = ['Anxiety', 'Negative\nEmotion', 'Insight', 'First Person\nSingular']
    
    means_control = [df[df['Group'] == 0][f].mean() for f in features]
    means_treatment = [df[df['Group'] == 1][f].mean() for f in features]
    
    x = np.arange(len(features))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, means_control, width, label='Control',
                    color=APA_COLORS['control'], edgecolor='black', linewidth=0.5)
    bars2 = ax.bar(x + width/2, means_treatment, width, label='Treatment',
                    color=APA_COLORS['treatment'], edgecolor='black', linewidth=0.5)
    
    ax.set_ylabel('Mean Percentage')
    ax.set_title('Key LIWC Features by Group', fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(feature_names, rotation=0)
    ax.legend(frameon=False)
    
    # 2. Anxiety words by MASR quartile
    ax = axes[0, 1]
    quartile_data = []
    for q in range(1, 5):
        data = df[df['MASR_Quartile'] == q]['liwc1_anx'].dropna()
        quartile_data.append(data)
    
    bp = ax.boxplot(quartile_data, patch_artist=True, showmeans=True)
    colors_gradient = ['#009E73', '#56B4E9', '#F0E442', '#D55E00']
    for patch, color in zip(bp['boxes'], colors_gradient):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    
    ax.set_xticklabels(['Q1\n(Low)', 'Q2', 'Q3', 'Q4\n(High)'])
    ax.set_xlabel('Math Anxiety Quartile')
    ax.set_ylabel('Anxiety Words (%)')
    ax.set_title('Anxiety Word Usage by Math Anxiety Level', fontweight='bold')
    
    # 3. Writing patterns (if clustering was done)
    if df_cluster is not None:
        ax = axes[1, 0]
        for cluster in df_cluster['cluster'].unique():
            cluster_data = df_cluster[df_cluster['cluster'] == cluster]
            ax.scatter(cluster_data['pca1'], cluster_data['pca2'], 
                      label=f'Pattern {cluster+1}', s=50, alpha=0.7,
                      edgecolors='black', linewidth=0.5)
        
        ax.set_xlabel('PCA Component 1')
        ax.set_ylabel('PCA Component 2')
        ax.set_title('Writing Pattern Clusters', fontweight='bold')
        ax.legend(frameon=False)
    
    # 4. Emotion change for those with both writings
    ax = axes[1, 1]
    has_both = (~df['liwc1_WC'].isna()) & (~df['liwc2_WC'].isna())
    
    if has_both.sum() > 20:
        emotion_t1 = df.loc[has_both, 'liwc1_negemo'] + df.loc[has_both, 'liwc1_anx']
        emotion_t2 = df.loc[has_both, 'liwc2_negemo'] + df.loc[has_both, 'liwc2_anx']
        
        # Separate by improvement status
        improved = df.loc[has_both, 'improved'] == 1
        
        ax.scatter(emotion_t1[improved], emotion_t2[improved], 
                  color=APA_COLORS['improved'], label='Improved', 
                  s=50, alpha=0.7, edgecolors='black', linewidth=0.5)
        ax.scatter(emotion_t1[~improved], emotion_t2[~improved], 
                  color=APA_COLORS['not_improved'], label='Not Improved', 
                  s=50, alpha=0.7, edgecolors='black', linewidth=0.5)
        
        # Add diagonal line
        lims = [ax.get_xlim()[0], ax.get_xlim()[1]]
        ax.plot(lims, lims, 'k-', alpha=0.3, zorder=0)
        
        ax.set_xlabel('Negative Emotion Time 1 (%)')
        ax.set_ylabel('Negative Emotion Time 2 (%)')
        ax.set_title('Change in Emotional Expression', fontweight='bold')
        ax.legend(frameon=False)
    
    plt.tight_layout()
    plt.savefig('figure2_liwc_analysis.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure2_liwc_analysis.pdf', dpi=300, bbox_inches='tight')
    plt.show()

# ===== MAIN EXECUTION =====

def main():
    """Main LIWC analysis pipeline"""
    
    # Load processed data
    df = pd.read_csv('k12_math_anxiety_processed.csv')
    print(f"Loaded data: {df.shape}")
    
    # Create composite indices
    df = create_liwc_composites(df)
    
    # Group comparisons
    treatment_results, anxiety_results = compare_liwc_by_groups(df)
    
    # Pattern analysis
    df_cluster = analyze_liwc_patterns(df)
    
    # Predictive analysis
    feature_importance = predict_improvement_from_liwc(df)
    
    # Create visualizations
    create_liwc_visualizations(df, df_cluster)
    
    # Save results
    df.to_csv('k12_liwc_analyzed.csv', index=False)
    
    # Export key results
    with pd.ExcelWriter('liwc_results.xlsx') as writer:
        treatment_results.to_excel(writer, sheet_name='Treatment_Effects', index=False)
        anxiety_results.to_excel(writer, sheet_name='Anxiety_Effects', index=False)
        if feature_importance is not None:
            feature_importance.to_excel(writer, sheet_name='Feature_Importance', index=False)
    
    print("\n✓ Analysis complete. Results saved to 'liwc_results.xlsx'")
    
    return df

if __name__ == "__main__":
    df = main()

#!/usr/bin/env python3
"""
K-12 Math Anxiety Intervention Study - LIWC Text Analysis
Phase 3: Deep analysis of linguistic patterns in expressive writing
Focus on treatment group only (control wrote about non-math topics)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

# APA-style plotting parameters
plt.style.use('seaborn-v0_8-whitegrid')

# APA-compatible color palette
APA_COLORS = {
    'low_anx': '#0072B2',      # Blue
    'high_anx': '#D55E00',     # Vermillion
    'q1': '#0072B2',           # Blue (lowest anxiety)
    'q2': '#56B4E9',           # Sky blue
    'q3': '#E69F00',           # Orange
    'q4': '#D55E00',           # Vermillion (highest anxiety)
    'improved': '#009E73',     # Green
    'not_improved': '#CC79A7', # Purple
    'accent': '#999999'        # Gray
}

plt.rcParams.update({
    'figure.figsize': (8, 6),
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 11,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 9,
    'font.family': 'sans-serif',
    'axes.spines.top': False,
    'axes.spines.right': False
})

# ===== 1. DATA PREPARATION =====

def prepare_liwc_data(df):
    """Prepare LIWC data focusing on treatment group"""
    
    # Filter treatment group only (who wrote about math)
    df_treat = df[df['Group'] == 1].copy()
    print(f"Treatment group N = {len(df_treat)}")
    print(f"With 1st writing: {(~df_treat['@1_English_Writing'].isna()).sum()}")
    print(f"With 2nd writing: {(~df_treat['@2_English_Writing'].isna()).sum()}")
    # Force convert all LIWC columns to numeric
        liwc_cols = [col for col in df_treat.columns if col.startswith('liwc1_') or col.startswith('liwc2_')]
        for col in liwc_cols:
            df_treat[col] = pd.to_numeric(df_treat[col], errors='coerce')
        
        return df_treat
    
    # Key LIWC variables of interest
    liwc_categories = {
        'emotional': ['liwc1_anx', 'liwc1_negemo', 'liwc1_posemo', 'liwc1_affect'],
        'cognitive': ['liwc1_cogproc', 'liwc1_insight', 'liwc1_cause', 'liwc1_discrep', 'liwc1_tentat', 'liwc1_certain'],
        'self_focus': ['liwc1_i', 'liwc1_we', 'liwc1_ppron'],
        'temporal': ['liwc1_focuspast', 'liwc1_focuspresent', 'liwc1_focusfuture'],
        'academic': ['liwc1_achieve', 'liwc1_work'],
        'linguistic': ['liwc1_WC', 'liwc1_WPS', 'liwc1_Analytic', 'liwc1_Clout', 'liwc1_Authentic', 'liwc1_Tone']
    }
    
    return df_treat, liwc_categories

# ===== 2. ANXIETY GROUP COMPARISONS =====

def analyze_by_anxiety_level(df_treat, liwc_categories):
    """Compare LIWC features across anxiety levels"""
    
    results = []
    
    # Define anxiety groups
    df_treat['anxiety_group'] = pd.cut(df_treat['MASR_Mean'], 
                                       bins=[0, df_treat['MASR_Mean'].quantile(0.25),
                                             df_treat['MASR_Mean'].quantile(0.75), 5],
                                       labels=['Low', 'Medium', 'High'])
    
    print("\n=== LIWC ANALYSIS BY ANXIETY LEVEL ===")
    print(f"\nAnxiety group distribution:")
    print(df_treat['anxiety_group'].value_counts())
    
    # Compare each LIWC category
    for category, vars in liwc_categories.items():
        print(f"\n{category.upper()} Features:")
        
        for var in vars:
            if var in df_treat.columns:
                # Get data for each anxiety group
                low = df_treat[df_treat['anxiety_group'] == 'Low'][var].dropna()
                high = df_treat[df_treat['anxiety_group'] == 'High'][var].dropna()
                
                if len(low) > 5 and len(high) > 5:  # Minimum sample size
                    # Mann-Whitney U test (robust to non-normality)
                    statistic, p_value = stats.mannwhitneyu(low, high, alternative='two-sided')
                    
                    # Effect size (rank-biserial correlation)
                    n1, n2 = len(low), len(high)
                    r = 1 - (2*statistic) / (n1*n2)
                    
                    # Cohen's d for comparison
                    pooled_std = np.sqrt(((n1-1)*low.std()**2 + (n2-1)*high.std()**2) / (n1+n2-2))
                    d = (high.mean() - low.mean()) / pooled_std if pooled_std > 0 else 0
                    
                    results.append({
                        'category': category,
                        'variable': var.replace('liwc1_', ''),
                        'low_mean': low.mean(),
                        'low_sd': low.std(),
                        'high_mean': high.mean(),
                        'high_sd': high.std(),
                        'p_value': p_value,
                        'effect_size_r': r,
                        'cohens_d': d,
                        'n_low': n1,
                        'n_high': n2
                    })
                    
                    if p_value < 0.05:
                        print(f"  {var.replace('liwc1_', '')}: Low={low.mean():.2f}±{low.std():.2f}, "
                              f"High={high.mean():.2f}±{high.std():.2f}, p={p_value:.3f}, d={d:.2f}")
    
    return pd.DataFrame(results), df_treat

# ===== 3. FEATURE IMPORTANCE FOR OUTCOMES =====

def analyze_liwc_outcome_relationships(df_treat, liwc_categories):
    """Identify LIWC features that predict improvement"""
    
    # Prepare data for modeling
    all_liwc_vars = []
    for vars in liwc_categories.values():
        all_liwc_vars.extend([v for v in vars if v in df_treat.columns])
    
    # Create dataset with complete cases
    feature_cols = all_liwc_vars + ['MASR_Mean', 'Working_Memory_Score']
    df_model = df_treat[feature_cols + ['Score_Change', 'improved_half_sd']].dropna()
    
    print(f"\n=== PREDICTIVE ANALYSIS ===")
    print(f"Complete cases for modeling: {len(df_model)}")
    
    if len(df_model) > 30:  # Minimum for meaningful analysis
        X = df_model[feature_cols]
        y_continuous = df_model['Score_Change']
        y_binary = df_model['improved_half_sd']
        
        # Random Forest for feature importance
        rf = RandomForestRegressor(n_estimators=100, random_state=42, max_depth=3)
        rf.fit(X, y_continuous)
        
        # Get feature importances
        importances = pd.DataFrame({
            'feature': feature_cols,
            'importance': rf.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\nTop 10 predictive features for score change:")
        print(importances.head(10))
        
        # Correlation analysis for interpretability
        correlations = []
        for col in all_liwc_vars:
            if col in df_model.columns:
                r, p = stats.spearmanr(df_model[col], df_model['Score_Change'])
                correlations.append({
                    'feature': col.replace('liwc1_', ''),
                    'correlation': r,
                    'p_value': p
                })
        
        corr_df = pd.DataFrame(correlations).sort_values('correlation', key=abs, ascending=False)
        
        return importances, corr_df, df_model
    else:
        print("Insufficient data for predictive modeling")
        return None, None, df_model

# ===== 4. LINGUISTIC PROFILES =====

def create_linguistic_profiles(df_treat, results_df):
    """Create linguistic profiles for anxiety groups"""
    
    # Select significant features
    sig_features = results_df[results_df['p_value'] < 0.05].sort_values('cohens_d', key=abs, ascending=False)
    
    if len(sig_features) > 0:
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # Prepare data for plotting
        features = sig_features.head(10)['variable'].values
        low_means = sig_features.head(10)['low_mean'].values
        high_means = sig_features.head(10)['high_mean'].values

        
        x = np.arange(len(features))
        width = 0.35
        
        # Create bars
        bars1 = ax.bar(x - width/2, low_means, width, label='Low Anxiety',
                       color=APA_COLORS['low_anx'], edgecolor='black', linewidth=0.5)
        bars2 = ax.bar(x + width/2, high_means, width, label='High Anxiety',
                       color=APA_COLORS['high_anx'], edgecolor='black', linewidth=0.5)
        
        # Formatting
        ax.set_xlabel('LIWC Category')
        ax.set_ylabel('Mean Percentage')
        ax.set_title('Linguistic Differences by Math Anxiety Level', fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(features, rotation=45, ha='right')
        ax.legend(frameon=False)
        
        # Add significance stars
        for i, row in enumerate(sig_features.head(10).itertuples()):
            if row.p_value < 0.001:
                stars = '***'
            elif row.p_value < 0.01:
                stars = '**'
            elif row.p_value < 0.05:
                stars = '*'
            else:
                stars = ''
            
            if stars:
                y_pos = max(low_means[i], high_means[i]) + 0.5
                ax.text(i, y_pos, stars, ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('figure2_linguistic_profiles.png', dpi=300, bbox_inches='tight')
        plt.savefig('figure2_linguistic_profiles.pdf', dpi=300, bbox_inches='tight')
        plt.show()

# ===== 5. WRITING EVOLUTION ANALYSIS =====

def analyze_writing_evolution(df_treat):
    """Analyze changes in writing patterns from Time 1 to Time 2"""
    
    # Identify students with both writings
    has_both = (~df_treat['@1_English_Writing'].isna()) & (~df_treat['@2_English_Writing'].isna())
    df_both = df_treat[has_both].copy()
    
    print(f"\n=== WRITING EVOLUTION ANALYSIS ===")
    print(f"Students with both writings: {len(df_both)}")
    
    if len(df_both) > 20:
        # Calculate changes in key LIWC variables
        key_vars = ['anx', 'negemo', 'posemo', 'insight', 'cause', 'i', 'focusfuture']
        
        changes = []
        for var in key_vars:
            if f'liwc1_{var}' in df_both.columns and f'liwc2_{var}' in df_both.columns:
                df_both[f'change_{var}'] = df_both[f'liwc2_{var}'] - df_both[f'liwc1_{var}']
                
                # Test if change is significant
                t_stat, p_val = stats.ttest_rel(df_both[f'liwc1_{var}'].dropna(), 
                                               df_both[f'liwc2_{var}'].dropna())
                
            # 对于每个变量，确保两个输入长度匹配
            if f'change_{var}' in df_both.columns and 'Score_Change' in df_both.columns:
                valid = df_both[[f'change_{var}', 'Score_Change']].dropna()
                if not valid.empty:
                    corr_val = stats.spearmanr(valid.iloc[:, 0], valid.iloc[:, 1])[0]
                else:
                    corr_val = np.nan
            else:
                corr_val = np.nan

            # 添加到列表中
            changes.append({
                'variable': var,
                'time1_mean': df_both[f'liwc1_{var}'].mean(),
                'time2_mean': df_both[f'liwc2_{var}'].mean(),
                'mean_change': df_both[f'change_{var}'].mean(),
                'p_value': p_val,
                'correlation_with_improvement': corr_val
            })

        
        changes_df = pd.DataFrame(changes)
        print("\nSignificant changes over time:")
        print(changes_df[changes_df['p_value'] < 0.05])
        
        return changes_df, df_both
    else:
        return None, None

# ===== 6. CLUSTERING ANALYSIS =====

def perform_clustering_analysis(df_model, all_liwc_vars):
    """Identify distinct writing patterns through clustering"""
    
    if df_model is not None and len(df_model) > 30:
        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(df_model[all_liwc_vars])
        
        # PCA for visualization
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(X_scaled)
        
        # K-means clustering
        kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
        clusters = kmeans.fit_predict(X_scaled)
        
        # Add cluster info to dataframe
        df_model['cluster'] = clusters
        
        # Analyze clusters
        print("\n=== CLUSTERING ANALYSIS ===")
        for i in range(3):
            cluster_data = df_model[df_model['cluster'] == i]
            print(f"\nCluster {i} (n={len(cluster_data)}):")
            print(f"  Mean anxiety: {cluster_data['MASR_Mean'].mean():.2f}")
            print(f"  Mean score change: {cluster_data['Score_Change'].mean():.2f}")
            print(f"  % improved: {cluster_data['improved_half_sd'].mean()*100:.1f}%")
        
        # Visualization
        fig, ax = plt.subplots(figsize=(8, 6))
        
        colors = [APA_COLORS['q1'], APA_COLORS['q3'], APA_COLORS['q4']]
        for i in range(3):
            mask = clusters == i
            ax.scatter(X_pca[mask, 0], X_pca[mask, 1], 
                      c=colors[i], label=f'Pattern {i+1}', 
                      alpha=0.6, edgecolor='black', linewidth=0.5)
        
        ax.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%} variance)')
        ax.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%} variance)')
        ax.set_title('Writing Pattern Clusters', fontweight='bold')
        ax.legend(frameon=False)
        
        plt.tight_layout()
        plt.savefig('figure3_clusters.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return df_model

# ===== 7. SUMMARY VISUALIZATION =====

def create_summary_heatmap(results_df):
    """Create heatmap of effect sizes for all comparisons"""
    
    if len(results_df) > 0:
        # Pivot data for heatmap
        heatmap_data = results_df.pivot_table(
            values='cohens_d', 
            index='variable', 
            columns='category',
            aggfunc='first'
        )
        
        # Create figure
        fig, ax = plt.subplots(figsize=(8, 10))
        
        # Create heatmap
        sns.heatmap(heatmap_data, 
                   cmap='RdBu_r', center=0, 
                   vmin=-0.8, vmax=0.8,
                   cbar_kws={'label': "Cohen's d"},
                   linewidths=0.5,
                   annot=True, fmt='.2f',
                   ax=ax)
        
        ax.set_title('Effect Sizes: High vs Low Math Anxiety', fontweight='bold', pad=20)
        ax.set_xlabel('')
        ax.set_ylabel('LIWC Feature')
        
        plt.tight_layout()
        plt.savefig('figure4_effect_sizes.png', dpi=300, bbox_inches='tight')
        plt.show()

# ===== MAIN EXECUTION =====

def main():
    """Main execution function"""
    
    # Load processed data
    print("Loading processed data...")
    df = pd.read_csv('k12_math_anxiety_processed.csv')
    
    # Prepare LIWC data
    df_treat, liwc_categories = prepare_liwc_data(df)
    
    # Analyze by anxiety level
    results_df, df_treat = analyze_by_anxiety_level(df_treat, liwc_categories)
    
    # Save detailed results
    if len(results_df) > 0:
        results_df.to_csv('liwc_anxiety_comparisons.csv', index=False)
        print("\n✓ Results saved to 'liwc_anxiety_comparisons.csv'")
    
    # Analyze outcome relationships
    importances, correlations, df_model = analyze_liwc_outcome_relationships(df_treat, liwc_categories)
    
    if correlations is not None:
        correlations.to_csv('liwc_outcome_correlations.csv', index=False)
        print("✓ Correlations saved to 'liwc_outcome_correlations.csv'")
    
    # Create linguistic profiles
    create_linguistic_profiles(df_treat, results_df)
    
    # Analyze writing evolution
    changes_df, df_both = analyze_writing_evolution(df_treat)
    
    if changes_df is not None:
        changes_df.to_csv('liwc_temporal_changes.csv', index=False)
        print("✓ Temporal changes saved to 'liwc_temporal_changes.csv'")
    
    # Clustering analysis
    all_liwc_vars = []
    for vars in liwc_categories.values():
        all_liwc_vars.extend([v for v in vars if v in df_treat.columns])
    
    df_clustered = perform_clustering_analysis(df_model, all_liwc_vars)
    
    # Summary visualization
    create_summary_heatmap(results_df)
    
    print("\n=== ANALYSIS COMPLETE ===")
    
    return df_treat, results_df, correlations

if __name__ == "__main__":
    df_treat, results_df, correlations = main()

pip install textblob


#!/usr/bin/env python3
"""
K-12 Math Anxiety LIWC Analysis - Part 1
Comprehensive analysis including word clouds, longitudinal changes, and baseline relationships
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from wordcloud import WordCloud
import re
from collections import Counter
from textblob import TextBlob
import warnings
warnings.filterwarnings('ignore')

# APA-style plotting
plt.style.use('seaborn-v0_8-whitegrid')
APA_COLORS = {
    'low_anx': '#0072B2',      # Blue
    'high_anx': '#D55E00',     # Vermillion
    'q1': '#0072B2',           # Blue (lowest anxiety)
    'q2': '#56B4E9',           # Sky blue
    'q3': '#E69F00',           # Orange
    'q4': '#D55E00',           # Vermillion (highest anxiety)
    'improved': '#009E73',     # Green
    'not_improved': '#CC79A7', # Purple
    'accent': '#999999'        # Gray
}

plt.rcParams.update({
    'figure.figsize': (10, 8),
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 11,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 9,
    'font.family': 'sans-serif',
    'axes.spines.top': False,
    'axes.spines.right': False
})

# ===== 1. DATA LOADING AND PREPARATION =====

def load_and_prepare_data():
    """Load and prepare data for analysis"""
    
    print("Loading data...")
    df = pd.read_csv('k12_math_anxiety_processed.csv')
    
    # Filter treatment group only
    df_treat = df[df['Group'] == 1].copy()
    print(f"\nTreatment group: N = {len(df_treat)}")
    print(f"With 1st writing: {(~df_treat['@1_English_Writing'].isna()).sum()}")
    print(f"With 2nd writing: {(~df_treat['@2_English_Writing'].isna()).sum()}")
    print(f"With both writings: {((~df_treat['@1_English_Writing'].isna()) & (~df_treat['@2_English_Writing'].isna())).sum()}")
    
    return df_treat

# ===== 2. WORD CLOUD ANALYSIS =====

def create_word_clouds(df_treat):
    """Create word clouds for high vs low anxiety students"""
    
    print("\n=== WORD CLOUD ANALYSIS ===")
    
    # Define anxiety groups using median split
    median_anxiety = df_treat['MASR_Mean'].median()
    high_anx = df_treat[df_treat['MASR_Mean'] > median_anxiety]
    low_anx = df_treat[df_treat['MASR_Mean'] <= median_anxiety]
    
    # Combine all writings for each group
    high_anx_text = ' '.join(high_anx['@1_English_Writing'].dropna().tolist() + 
                            high_anx['@2_English_Writing'].dropna().tolist())
    low_anx_text = ' '.join(low_anx['@1_English_Writing'].dropna().tolist() + 
                           low_anx['@2_English_Writing'].dropna().tolist())
    
    # Clean text
    def clean_text(text):
        text = text.lower()
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\d+', '', text)
        return text
    
    high_anx_text = clean_text(high_anx_text)
    low_anx_text = clean_text(low_anx_text)
    
    # Create figure
    fig, axes = plt.subplots(1, 2, figsize=(14, 6))
    
    # High anxiety word cloud
    wc_high = WordCloud(width=800, height=400, 
                        background_color='white',
                        colormap='Reds',
                        max_words=100).generate(high_anx_text)
    axes[0].imshow(wc_high, interpolation='bilinear')
    axes[0].set_title('High Math Anxiety Students', fontweight='bold', fontsize=12)
    axes[0].axis('off')
    
    # Low anxiety word cloud
    wc_low = WordCloud(width=800, height=400, 
                       background_color='white',
                       colormap='Blues',
                       max_words=100).generate(low_anx_text)
    axes[1].imshow(wc_low, interpolation='bilinear')
    axes[1].set_title('Low Math Anxiety Students', fontweight='bold', fontsize=12)
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.savefig('figure1_wordclouds.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Word frequency analysis
    print("\n--- Word Frequency Analysis ---")
    
    # Get most common words for each group
    def get_top_words(text, n=20):
        words = text.split()
        # Remove common stop words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
                     'of', 'with', 'is', 'am', 'are', 'was', 'were', 'been', 'be', 'have',
                     'has', 'had', 'do', 'does', 'did', 'will', 'can', 'could', 'should',
                     'would', 'may', 'might', 'it', 'i', 'me', 'my', 'we', 'you', 'that', 'this'}
        words = [w for w in words if w not in stop_words and len(w) > 2]
        return Counter(words).most_common(n)
    
    high_top = get_top_words(high_anx_text)
    low_top = get_top_words(low_anx_text)
    
    print("\nTop 20 words - High Anxiety:")
    for word, count in high_top[:20]:
        print(f"  {word}: {count}")
    
    print("\nTop 20 words - Low Anxiety:")
    for word, count in low_top[:20]:
        print(f"  {word}: {count}")
    
    return high_anx_text, low_anx_text

# ===== 3. COMPREHENSIVE LIWC ANALYSIS =====

def analyze_liwc_features(df_treat):
    """Analyze all LIWC features by anxiety level"""
    
    print("\n=== COMPREHENSIVE LIWC ANALYSIS ===")
    
    # Get all LIWC variables (those starting with 'liwc1_')
    liwc_vars = [col for col in df_treat.columns if col.startswith('liwc1_')]
    
    # Key categories to highlight
    key_categories = {
        'Emotional': ['liwc1_anx', 'liwc1_negemo', 'liwc1_posemo', 'liwc1_affect', 
                     'liwc1_sad', 'liwc1_anger'],
        'Cognitive': ['liwc1_cogproc', 'liwc1_insight', 'liwc1_cause', 'liwc1_discrep', 
                     'liwc1_tentat', 'liwc1_certain', 'liwc1_differ'],
        'Pronouns': ['liwc1_i', 'liwc1_we', 'liwc1_you', 'liwc1_shehe', 'liwc1_they'],
        'Temporal': ['liwc1_focuspast', 'liwc1_focuspresent', 'liwc1_focusfuture'],
        'Achievement': ['liwc1_achieve', 'liwc1_work', 'liwc1_reward', 'liwc1_risk'],
        'Social': ['liwc1_social', 'liwc1_family', 'liwc1_friend', 'liwc1_affiliation']
    }
    
    # Create anxiety groups
    median_anx = df_treat['MASR_Mean'].median()
    quartiles = df_treat['MASR_Mean'].quantile([0.25, 0.5, 0.75])
    
    results = []
    
    # Analyze each LIWC variable
    for category, vars_list in key_categories.items():
        print(f"\n{category} Features:")
        
        for var in vars_list:
            if var in df_treat.columns:
                # High vs Low comparison
                high = pd.to_numeric(df_treat[df_treat['MASR_Mean'] > median_anx][var], errors='coerce').dropna()
                low = pd.to_numeric(df_treat[df_treat['MASR_Mean'] <= median_anx][var], errors='coerce').dropna()

                
                if len(high) > 5 and len(low) > 5:
                    # Test for normality
                    _, p_norm_high = stats.shapiro(high) if len(high) > 3 else (None, 1)
                    _, p_norm_low = stats.shapiro(low) if len(low) > 3 else (None, 1)
                    
                    # Choose appropriate test
                    if p_norm_high and p_norm_low and p_norm_high > 0.05 and p_norm_low > 0.05:
                        stat, p_val = stats.ttest_ind(high, low)
                        test_type = 't-test'
                    else:
                        stat, p_val = stats.mannwhitneyu(high, low)
                        test_type = 'Mann-Whitney'
                    
                    # Calculate effect size
                    cohens_d = (high.mean() - low.mean()) / np.sqrt(((len(high)-1)*high.std()**2 + 
                                                                     (len(low)-1)*low.std()**2) / 
                                                                    (len(high)+len(low)-2))
                    
                    results.append({
                        'category': category,
                        'variable': var.replace('liwc1_', ''),
                        'low_mean': low.mean(),
                        'low_sd': low.std(),
                        'high_mean': high.mean(),
                        'high_sd': high.std(),
                        'p_value': p_val,
                        'cohens_d': cohens_d,
                        'test_type': test_type
                    })
                    
                    if p_val < 0.05:
                        print(f"  {var.replace('liwc1_', '')}: "
                              f"Low={low.mean():.2f}±{low.std():.2f}, "
                              f"High={high.mean():.2f}±{high.std():.2f}, "
                              f"p={p_val:.3f}, d={cohens_d:.2f}")
    
    results_df = pd.DataFrame(results)
    
    # Find additional significant variables not in key categories
    print("\n=== Additional Significant Variables ===")
    all_results = []
    
    for var in liwc_vars:
        if var not in [v for vars_list in key_categories.values() for v in vars_list]:
            high_raw = df_treat[df_treat['MASR_Mean'] > median_anx][var]
            low_raw = df_treat[df_treat['MASR_Mean'] <= median_anx][var]

            high = pd.to_numeric(high_raw, errors='coerce').dropna()
            low = pd.to_numeric(low_raw, errors='coerce').dropna()

            
            if len(high) > 5 and len(low) > 5:
                stat, p_val = stats.mannwhitneyu(high, low)
                if p_val < 0.05:
                    print(f"{var.replace('liwc1_', '')}: p={p_val:.3f}")
    
    return results_df

# ===== 4. BASELINE RELATIONSHIPS =====

def analyze_baseline_relationships(df_treat):
    """Analyze relationships between baseline measures and writing patterns"""
    
    print("\n=== BASELINE RELATIONSHIPS ANALYSIS ===")
    
    # Select key LIWC variables
    key_liwc = ['liwc1_anx', 'liwc1_negemo', 'liwc1_posemo', 'liwc1_i', 
                'liwc1_cogproc', 'liwc1_insight', 'liwc1_focusfuture']
    
    # Create correlation matrix
    baseline_vars = ['MASR_Mean', 'Working_Memory_Score', 'ExamAnxiety_Mean']
    
    correlations = []
    
    print("\n1. Math Anxiety (MASR) Correlations:")
    for liwc_var in key_liwc:
        if liwc_var in df_treat.columns:
            data = df_treat[['MASR_Mean', liwc_var]].dropna()
            if len(data) > 20:
                r, p = stats.spearmanr(data['MASR_Mean'], data[liwc_var])
                correlations.append({
                    'baseline': 'MASR',
                    'liwc_var': liwc_var.replace('liwc1_', ''),
                    'correlation': r,
                    'p_value': p
                })
                if p < 0.05:
                    print(f"  {liwc_var.replace('liwc1_', '')}: r={r:.3f}, p={p:.3f}")
    
    print("\n2. Working Memory Correlations:")
    for liwc_var in key_liwc:
        if liwc_var in df_treat.columns:
            data = df_treat[['Working_Memory_Score', liwc_var]].dropna()
            if len(data) > 20:
                r, p = stats.spearmanr(data['Working_Memory_Score'], data[liwc_var])
                correlations.append({
                    'baseline': 'Working Memory',
                    'liwc_var': liwc_var.replace('liwc1_', ''),
                    'correlation': r,
                    'p_value': p
                })
                if p < 0.05:
                    print(f"  {liwc_var.replace('liwc1_', '')}: r={r:.3f}, p={p:.3f}")
    
    # Create visualization
    corr_df = pd.DataFrame(correlations)
    sig_corr = corr_df[corr_df['p_value'] < 0.05]
    
    if len(sig_corr) > 0:
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Prepare data for heatmap
        pivot_corr = sig_corr.pivot(index='liwc_var', columns='baseline', values='correlation')
        
        sns.heatmap(pivot_corr, cmap='RdBu_r', center=0, 
                   annot=True, fmt='.3f', 
                   cbar_kws={'label': 'Spearman Correlation'},
                   ax=ax, linewidths=0.5)
        
        ax.set_title('Significant Correlations: Baseline Measures & Language Features', 
                    fontweight='bold')
        ax.set_xlabel('')
        ax.set_ylabel('LIWC Feature')
        
        plt.tight_layout()
        plt.savefig('figure2_baseline_correlations.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    return corr_df

# ===== 5. LONGITUDINAL ANALYSIS =====

def analyze_longitudinal_changes(df_treat):
    """Analyze changes in writing patterns for students with both samples"""
    
    print("\n=== LONGITUDINAL ANALYSIS ===")
    
    # Filter students with both writings
    has_both = (~df_treat['@1_English_Writing'].isna()) & (~df_treat['@2_English_Writing'].isna())
    df_both = df_treat[has_both].copy()
    
    print(f"Students with both writings: {len(df_both)}")
    
    if len(df_both) < 20:
        print("Insufficient data for longitudinal analysis")
        return None
    
    # Key variables to track
    track_vars = ['anx', 'negemo', 'posemo', 'i', 'cogproc', 'insight', 
                  'cause', 'focusfuture', 'achieve', 'certain']
    
    changes = []
    
    for var in track_vars:
        var1 = f'liwc1_{var}'
        var2 = f'liwc2_{var}'
        
        if var1 in df_both.columns and var2 in df_both.columns:
            # Calculate change
            df_both[f'change_{var}'] = df_both[var2] - df_both[var1]
            
            # Paired t-test
            data_pairs = df_both[[var1, var2]].dropna()
            if len(data_pairs) > 10:
                t_stat, p_val = stats.ttest_rel(data_pairs[var1], data_pairs[var2])
                
                # Effect size for paired data
                d = (data_pairs[var2].mean() - data_pairs[var1].mean()) / data_pairs[var1].std()
                
                # Correlation with improvement
                if 'Score_Change' in df_both.columns:
                    change_improve_corr = stats.spearmanr(
                        df_both[f'change_{var}'].dropna(),
                        df_both.loc[df_both[f'change_{var}'].notna(), 'Score_Change']
                    )
                else:
                    change_improve_corr = (np.nan, np.nan)
                
                changes.append({
                    'variable': var,
                    'time1_mean': data_pairs[var1].mean(),
                    'time1_sd': data_pairs[var1].std(),
                    'time2_mean': data_pairs[var2].mean(),
                    'time2_sd': data_pairs[var2].std(),
                    'mean_change': df_both[f'change_{var}'].mean(),
                    'p_value': p_val,
                    'cohens_d': d,
                    'corr_with_improvement': change_improve_corr[0],
                    'corr_p_value': change_improve_corr[1]
                })
    
    changes_df = pd.DataFrame(changes)
    
    # Print significant changes
    print("\nSignificant temporal changes:")
    sig_changes = changes_df[changes_df['p_value'] < 0.05]
    for _, row in sig_changes.iterrows():
        print(f"{row['variable']}: {row['time1_mean']:.2f} → {row['time2_mean']:.2f} "
              f"(p={row['p_value']:.3f}, d={row['cohens_d']:.2f})")
    
    # Visualize changes
    if len(sig_changes) > 0:
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Create paired plot
        x = np.arange(len(sig_changes))
        width = 0.35
        
        bars1 = ax.bar(x - width/2, sig_changes['time1_mean'], width, 
                       label='Time 1', color=APA_COLORS['accent'], 
                       yerr=sig_changes['time1_sd'], capsize=5,
                       edgecolor='black', linewidth=0.5)
        bars2 = ax.bar(x + width/2, sig_changes['time2_mean'], width,
                       label='Time 2', color=APA_COLORS['improved'], 
                       yerr=sig_changes['time2_sd'], capsize=5,
                       edgecolor='black', linewidth=0.5)
        
        ax.set_xlabel('LIWC Feature')
        ax.set_ylabel('Mean Percentage')
        ax.set_title('Significant Temporal Changes in Language Features', fontweight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(sig_changes['variable'], rotation=45, ha='right')
        ax.legend(frameon=False)
        
        # Add significance stars
        for i, (_, row) in enumerate(sig_changes.iterrows()):
            y_pos = max(row['time1_mean'] + row['time1_sd'], 
                       row['time2_mean'] + row['time2_sd']) + 0.5
            if row['p_value'] < 0.001:
                stars = '***'
            elif row['p_value'] < 0.01:
                stars = '**'
            else:
                stars = '*'
            ax.text(i, y_pos, stars, ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('figure3_temporal_changes.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    # Analyze change patterns by anxiety level
    print("\n=== Change Patterns by Anxiety Level ===")
    
    # Split by anxiety level
    median_anx = df_both['MASR_Mean'].median()
    
    for var in ['anx', 'negemo', 'i']:
        if f'change_{var}' in df_both.columns:
            high_anx_change = df_both[df_both['MASR_Mean'] > median_anx][f'change_{var}'].mean()
            low_anx_change = df_both[df_both['MASR_Mean'] <= median_anx][f'change_{var}'].mean()
            
            print(f"\n{var} change:")
            print(f"  High anxiety: {high_anx_change:.2f}")
            print(f"  Low anxiety: {low_anx_change:.2f}")
    
    return changes_df

# ===== 6. WORKING MEMORY MODERATION =====

def analyze_working_memory_moderation(df_treat):
    """Analyze how working memory moderates language patterns"""
    
    print("\n=== WORKING MEMORY MODERATION ANALYSIS ===")
    
    # Create 2x2 groups
    median_anx = df_treat['MASR_Mean'].median()
    median_wm = df_treat['Working_Memory_Score'].median()
    
    df_treat['group_2x2'] = ''
    df_treat.loc[(df_treat['MASR_Mean'] <= median_anx) & 
                 (df_treat['Working_Memory_Score'] <= median_wm), 'group_2x2'] = 'Low Anx/Low WM'
    df_treat.loc[(df_treat['MASR_Mean'] <= median_anx) & 
                 (df_treat['Working_Memory_Score'] > median_wm), 'group_2x2'] = 'Low Anx/High WM'
    df_treat.loc[(df_treat['MASR_Mean'] > median_anx) & 
                 (df_treat['Working_Memory_Score'] <= median_wm), 'group_2x2'] = 'High Anx/Low WM'
    df_treat.loc[(df_treat['MASR_Mean'] > median_anx) & 
                 (df_treat['Working_Memory_Score'] > median_wm), 'group_2x2'] = 'High Anx/High WM'
    
    # Remove empty groups
    df_treat = df_treat[df_treat['group_2x2'] != '']
    
    print("\nGroup sizes:")
    print(df_treat['group_2x2'].value_counts())
    
    # Analyze key variables by group
    key_vars = ['liwc1_anx', 'liwc1_cogproc', 'liwc1_insight', 'liwc1_i']
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.flatten()
    
    for i, var in enumerate(key_vars):
        if var in df_treat.columns:
            ax = axes[i]
            
            # Get data for each group
            group_data = []
            group_labels = []
            
            for group in ['Low Anx/Low WM', 'Low Anx/High WM', 
                         'High Anx/Low WM', 'High Anx/High WM']:
                data = df_treat[df_treat['group_2x2'] == group][var].dropna()
                if len(data) > 0:
                    group_data.append(data)
                    group_labels.append(f"{group}\n(n={len(data)})")
            
            # Create box plot
            bp = ax.boxplot(group_data, labels=group_labels, patch_artist=True)
            
            # Color boxes
            colors = [APA_COLORS['low_anx'], APA_COLORS['low_anx'], 
                     APA_COLORS['high_anx'], APA_COLORS['high_anx']]
            for patch, color in zip(bp['boxes'], colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            ax.set_ylabel(var.replace('liwc1_', '').upper())
            ax.set_title(f'{var.replace("liwc1_", "").capitalize()} by Anxiety × Working Memory', 
                        fontweight='bold')
            ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('figure4_wm_moderation.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Statistical tests for interaction
    print("\n=== Interaction Effects ===")
    
    for var in key_vars:
        if var in df_treat.columns:
            # Prepare data for 2-way ANOVA
            data = df_treat[[var, 'MASR_Mean', 'Working_Memory_Score']].dropna()
            if len(data) > 30:
                data['high_anx'] = (data['MASR_Mean'] > median_anx).astype(int)
                data['high_wm'] = (data['Working_Memory_Score'] > median_wm).astype(int)
                
                # Simple interaction test using correlation
                # (More sophisticated analysis would use factorial ANOVA)
                interaction_score = data['high_anx'] * data['high_wm']
                r, p = stats.spearmanr(interaction_score, data[var])
                
                if p < 0.05:
                    print(f"{var.replace('liwc1_', '')}: Interaction effect p={p:.3f}")

# ===== MAIN EXECUTION =====

def main():
    """Execute all analyses"""
    
    # Load data
    df_treat = load_and_prepare_data()
    
    # 1. Word cloud analysis
    high_text, low_text = create_word_clouds(df_treat)
    
    # 2. Comprehensive LIWC analysis
    results_df = analyze_liwc_features(df_treat)
    results_df.to_csv('liwc_comprehensive_results.csv', index=False)
    print("\n✓ Results saved to 'liwc_comprehensive_results.csv'")
    
    # 3. Baseline relationships
    corr_df = analyze_baseline_relationships(df_treat)
    corr_df.to_csv('baseline_correlations.csv', index=False)
    
    # 4. Longitudinal analysis
    changes_df = analyze_longitudinal_changes(df_treat)
    if changes_df is not None:
        changes_df.to_csv('longitudinal_changes.csv', index=False)
        print("✓ Longitudinal results saved")
    
    # 5. Working memory moderation
    analyze_working_memory_moderation(df_treat)
    
    print("\n=== ANALYSIS COMPLETE ===")
    
    return df_treat, results_df

if __name__ == "__main__":
    df_treat, results = main()

#!/usr/bin/env python3
"""
K-12 Math Anxiety Intervention Study - LIWC Analysis Part 1
Feature Analysis by Math Anxiety and Working Memory
Following MHC Dataset Poster methodology
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from wordcloud import WordCloud
from collections import Counter
import re
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# APA-style plotting parameters
plt.style.use('seaborn-v0_8-whitegrid')

# APA-compatible color palette
APA_COLORS = {
    'q1': '#0072B2',      # Blue (lowest anxiety)
    'q2': '#56B4E9',      # Sky blue
    'q3': '#E69F00',      # Orange
    'q4': '#D55E00',      # Vermillion (highest anxiety)
    'low': '#0072B2',     # Blue
    'high': '#D55E00',    # Vermillion
    'accent': '#999999'   # Gray
}

plt.rcParams.update({
    'figure.figsize': (10, 8),
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 11,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 9,
    'font.family': 'sans-serif',
    'axes.spines.top': False,
    'axes.spines.right': False
})

# ===== 1. DATA PREPARATION =====

def prepare_data_for_analysis(df):
    """
    Prepare data for analysis, combining first and second writing samples
    for treatment group to increase sample size
    """
    
    # Filter treatment group only
    df_treat = df[df['Group'] == 1].copy()
    
    print("=== DATA PREPARATION ===")
    print(f"Treatment group participants: {len(df_treat)}")
    
    # Create a list to store all writing samples
    all_samples = []
    
    # Process first writing samples
    first_samples = df_treat[~df_treat['@1_English_Writing'].isna()].copy()
    first_samples['writing_time'] = 1
    first_samples['sample_id'] = first_samples['ID'].astype(str) + '_T1'
    
    # Process second writing samples  
    second_samples = df_treat[~df_treat['@2_English_Writing'].isna()].copy()
    second_samples['writing_time'] = 2
    second_samples['sample_id'] = second_samples['ID'].astype(str) + '_T2'
    
    print(f"First writing samples: {len(first_samples)}")
    print(f"Second writing samples: {len(second_samples)}")
    
    # Rename LIWC columns for consistency
    liwc1_mapping = {col: col.replace('liwc1_', '') for col in first_samples.columns if col.startswith('liwc1_')}
    liwc2_mapping = {col: col.replace('liwc2_', '') for col in second_samples.columns if col.startswith('liwc2_')}
    
    first_samples.rename(columns=liwc1_mapping, inplace=True)
    second_samples.rename(columns=liwc2_mapping, inplace=True)
    
    # Combine samples
    common_cols = list(set(first_samples.columns) & set(second_samples.columns))
    df_combined = pd.concat([first_samples[common_cols], second_samples[common_cols]], ignore_index=True)
    
    print(f"Total writing samples for analysis: {len(df_combined)}")
    
    # Add anxiety quartiles
    df_combined['masr_quartile'] = pd.qcut(df_combined['MASR_Mean'], 
                                           q=4, 
                                           labels=['Q1', 'Q2', 'Q3', 'Q4'])
    
    # Add anxiety level (high/low based on median)
    median_anxiety = df_combined['MASR_Mean'].median()
    df_combined['anxiety_level'] = df_combined['MASR_Mean'].apply(
        lambda x: 'High' if x > median_anxiety else 'Low'
    )
    
    # Add working memory groups
    median_wm = df_combined['Working_Memory_Score'].median()
    df_combined['wm_group'] = df_combined['Working_Memory_Score'].apply(
        lambda x: 'High WM' if x > median_wm else 'Low WM'
    )
    
    return df_combined, df_treat

# ===== 2. WORD CLOUD ANALYSIS =====

def create_anxiety_wordclouds(df_combined):
    """
    Create word clouds comparing high vs low anxiety students' writing
    """
    
    print("\n=== WORD CLOUD ANALYSIS ===")
    
    # Combine all text for high and low anxiety groups
    high_anxiety_text = ' '.join(df_combined[df_combined['anxiety_level'] == 'High']['@1_English_Writing'].dropna().astype(str))
    low_anxiety_text = ' '.join(df_combined[df_combined['anxiety_level'] == 'Low']['@1_English_Writing'].dropna().astype(str))
    
    # Create figure with two subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # High anxiety word cloud
    if high_anxiety_text:
        wc_high = WordCloud(width=800, height=400, 
                           background_color='white',
                           colormap='Reds',
                           max_words=100).generate(high_anxiety_text)
        ax1.imshow(wc_high, interpolation='bilinear')
        ax1.set_title('High Math Anxiety', fontsize=14, fontweight='bold', pad=20)
        ax1.axis('off')
    
    # Low anxiety word cloud
    if low_anxiety_text:
        wc_low = WordCloud(width=800, height=400, 
                          background_color='white',
                          colormap='Blues',
                          max_words=100).generate(low_anxiety_text)
        ax2.imshow(wc_low, interpolation='bilinear')
        ax2.set_title('Low Math Anxiety', fontsize=14, fontweight='bold', pad=20)
        ax2.axis('off')
    
    plt.suptitle('Word Clouds by Math Anxiety Level', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('figure1_wordclouds.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure1_wordclouds.pdf', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Word frequency analysis
    print("\nTop 20 words by anxiety level:")
    
    def get_word_freq(text, top_n=20):
        """Get word frequency excluding common stop words"""
        words = re.findall(r'\b[a-z]+\b', text.lower())
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
                     'of', 'with', 'by', 'from', 'is', 'am', 'are', 'was', 'were', 'been',
                     'be', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would',
                     'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that',
                     'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'my'}
        words = [w for w in words if w not in stop_words and len(w) > 2]
        return Counter(words).most_common(top_n)
    
    if high_anxiety_text:
        print("\nHigh Anxiety:")
        for word, count in get_word_freq(high_anxiety_text):
            print(f"  {word}: {count}")
    
    if low_anxiety_text:
        print("\nLow Anxiety:")
        for word, count in get_word_freq(low_anxiety_text):
            print(f"  {word}: {count}")

# ===== 3. ENHANCED QUARTILE HEATMAP (FROM MHC POSTER) =====

def plot_enhanced_heatmap_by_quartile(df, features, group_col='masr_quartile'):
    """
    Create enhanced Z-score heatmap showing language features across MASR quartiles,
    ordered by Q4-Q1 difference for better interpretability.
    Directly adapted from MHC Dataset Poster Part 6
    """
    
    print("\n=== ENHANCED MASR QUARTILE HEATMAP ANALYSIS ===")
    
    # Calculate group means and Z-scores
    group_means = df.groupby(group_col)[features].mean()
    
    # Calculate Z-scores relative to overall mean and std
    overall_means = df[features].mean()
    overall_stds = df[features].std()
    
    z_scores = (group_means - overall_means) / overall_stds
    
    # Calculate Q4-Q1 difference for ordering
    if 'Q1' in z_scores.index and 'Q4' in z_scores.index:
        q4_q1_diff = z_scores.loc['Q4'] - z_scores.loc['Q1']
        # Order features by absolute difference
        ordered_features = q4_q1_diff.abs().sort_values(ascending=False).index.tolist()
        z_scores = z_scores[ordered_features]
    
    # Create the heatmap
    plt.figure(figsize=(12, 8))
    
    # Create custom colormap
    cmap = sns.diverging_palette(250, 10, as_cmap=True)
    
    # Plot heatmap
    sns.heatmap(z_scores.T, 
                cmap=cmap, 
                center=0,
                vmin=-2, vmax=2,
                annot=True, 
                fmt='.2f',
                cbar_kws={'label': 'Z-Score'},
                linewidths=0.5,
                linecolor='gray')
    
    plt.title('Language Features by Math Anxiety Quartile\n(Z-scores relative to overall mean)', 
             fontsize=14, fontweight='bold', pad=20)
    plt.xlabel('Math Anxiety Quartile', fontsize=12)
    plt.ylabel('LIWC Features', fontsize=12)
    
    # Highlight significant differences
    ax = plt.gca()
    for i, feature in enumerate(ordered_features):
        if 'Q1' in z_scores.index and 'Q4' in z_scores.index:
            diff = z_scores.loc['Q4', feature] - z_scores.loc['Q1', feature]
            if abs(diff) > 0.5:  # Meaningful effect size
                ax.add_patch(plt.Rectangle((0, i-0.5), 4, 1, 
                                         fill=False, edgecolor='black', 
                                         linewidth=2))
    
    plt.tight_layout()
    plt.savefig('figure2_quartile_heatmap.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure2_quartile_heatmap.pdf', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print summary statistics
    print("\nFeatures with largest Q4-Q1 differences:")
    if 'Q1' in z_scores.index and 'Q4' in z_scores.index:
        q4_q1_diff = z_scores.loc['Q4'] - z_scores.loc['Q1']
        for feature in q4_q1_diff.abs().sort_values(ascending=False).head(10).index:
            diff = q4_q1_diff[feature]
            print(f"  {feature}: {diff:.3f} (Q1={z_scores.loc['Q1', feature]:.2f}, "
                  f"Q4={z_scores.loc['Q4', feature]:.2f})")

# ===== 4. STATISTICAL COMPARISONS =====

def comprehensive_feature_analysis(df_combined):
    """
    Comprehensive statistical analysis of LIWC features by anxiety level
    """
    
    print("\n=== COMPREHENSIVE FEATURE ANALYSIS ===")
    
    # Define all LIWC features to analyze
    liwc_features = ['WC', 'WPS', 'Analytic', 'Clout', 'Authentic', 'Tone',
                    'i', 'we', 'you', 'shehe', 'they', 
                    'posemo', 'negemo', 'anx', 'anger', 'sad',
                    'cogproc', 'insight', 'cause', 'discrep', 'tentat', 'certain',
                    'achieve', 'power', 'reward', 'risk',
                    'focuspast', 'focuspresent', 'focusfuture',
                    'work', 'leisure', 'home', 'money', 'relig', 'death']
    
    # Filter to available features
    available_features = [f for f in liwc_features if f in df_combined.columns]
    print(f"Analyzing {len(available_features)} LIWC features")
    
    # Perform comparisons
    results = []
    
    for feature in available_features:
        # High vs Low anxiety comparison
        high = df_combined[df_combined['anxiety_level'] == 'High'][feature].dropna()
        low = df_combined[df_combined['anxiety_level'] == 'Low'][feature].dropna()
        
        if len(high) > 5 and len(low) > 5:
            # Test normality
            _, p_norm_high = stats.shapiro(high) if len(high) > 3 else (np.nan, 0)
            _, p_norm_low = stats.shapiro(low) if len(low) > 3 else (np.nan, 0)
            
            # Choose test based on normality
            if p_norm_high < 0.05 or p_norm_low < 0.05:
                stat, p_value = stats.mannwhitneyu(high, low)
                test_used = 'Mann-Whitney U'
                # Calculate effect size (rank-biserial correlation)
                n1, n2 = len(high), len(low)
                effect_size = 1 - (2*stat) / (n1*n2)
            else:
                stat, p_value = stats.ttest_ind(high, low)
                test_used = 't-test'
                # Calculate Cohen's d
                pooled_std = np.sqrt(((len(high)-1)*high.std()**2 + (len(low)-1)*low.std()**2) / (len(high)+len(low)-2))
                effect_size = (high.mean() - low.mean()) / pooled_std if pooled_std > 0 else 0
            
            results.append({
                'feature': feature,
                'high_mean': high.mean(),
                'high_sd': high.std(),
                'low_mean': low.mean(), 
                'low_sd': low.std(),
                'statistic': stat,
                'p_value': p_value,
                'effect_size': effect_size,
                'test': test_used,
                'n_high': len(high),
                'n_low': len(low)
            })
    
    # Convert to DataFrame
    results_df = pd.DataFrame(results)
    
    # Apply FDR correction
    from statsmodels.stats.multitest import multipletests
    if len(results_df) > 0:
        _, pvals_corrected, _, _ = multipletests(results_df['p_value'], method='fdr_bh')
        results_df['p_adjusted'] = pvals_corrected
        results_df['significant'] = results_df['p_adjusted'] < 0.05
    
    # Sort by effect size
    results_df = results_df.sort_values('effect_size', key=abs, ascending=False)
    
    # Print significant results
    print("\nSignificant differences after FDR correction:")
    sig_results = results_df[results_df['significant']]
    for _, row in sig_results.iterrows():
        print(f"\n{row['feature']}:")
        print(f"  High anxiety: {row['high_mean']:.2f} ± {row['high_sd']:.2f}")
        print(f"  Low anxiety: {row['low_mean']:.2f} ± {row['low_sd']:.2f}")
        print(f"  Effect size: {row['effect_size']:.3f}, p_adj = {row['p_adjusted']:.4f}")
    
    return results_df

# ===== 5. WORKING MEMORY MODERATION ANALYSIS =====

def analyze_wm_moderation(df_combined):
    """
    Analyze how working memory moderates the relationship between
    anxiety and language features
    """
    
    print("\n=== WORKING MEMORY MODERATION ANALYSIS ===")
    
    # Create 2x2 groups
    df_combined['anxiety_wm_group'] = df_combined['anxiety_level'] + ' / ' + df_combined['wm_group']
    
    # Key features to analyze
    key_features = ['anx', 'negemo', 'posemo', 'cogproc', 'insight', 'i', 'focusfuture']
    available_key = [f for f in key_features if f in df_combined.columns]
    
    # Create visualization
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    axes = axes.flatten()
    
    for idx, feature in enumerate(available_key[:8]):
        ax = axes[idx]
        
        # Calculate means for each group
        group_means = df_combined.groupby('anxiety_wm_group')[feature].agg(['mean', 'sem'])
        
        # Define order and colors
        group_order = ['Low / Low WM', 'Low / High WM', 'High / Low WM', 'High / High WM']
        colors = ['#3498DB', '#2ECC71', '#E74C3C', '#F39C12']
        
        # Create bar plot
        positions = range(len(group_order))
        for i, group in enumerate(group_order):
            if group in group_means.index:
                ax.bar(i, group_means.loc[group, 'mean'], 
                      yerr=group_means.loc[group, 'sem'],
                      color=colors[i], edgecolor='black', linewidth=0.5,
                      capsize=5)
        
        ax.set_title(feature, fontweight='bold')
        ax.set_xticks(positions)
        ax.set_xticklabels(['L/L', 'L/H', 'H/L', 'H/H'], rotation=0)
        ax.set_ylabel('Mean %')
        
        if idx >= 4:
            ax.set_xlabel('Anxiety / WM')
    
    # Remove empty subplots
    for idx in range(len(available_key), len(axes)):
        fig.delaxes(axes[idx])
    
    plt.suptitle('Language Features by Anxiety Level and Working Memory', 
                fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig('figure3_wm_moderation.png', dpi=300, bbox_inches='tight')
    plt.show()

# ===== 6. CORRELATION WITH BASELINE MEASURES =====

def analyze_baseline_correlations(df_combined):
    """
    Analyze correlations between baseline measures (MASR, WM) and language features
    """
    
    print("\n=== BASELINE CORRELATIONS ANALYSIS ===")
    
    # Select key features
    key_features = ['anx', 'negemo', 'posemo', 'cogproc', 'insight', 'cause', 
                   'i', 'we', 'focuspast', 'focuspresent', 'focusfuture']
    available_features = [f for f in key_features if f in df_combined.columns]
    
    # Calculate correlations
    correlations = []

    for feature in available_features:
        # Correlation with MASR
        valid1 = df_combined[['MASR_Mean', feature]].dropna()
        if len(valid1) > 5:
            masr_corr, masr_p = stats.spearmanr(valid1['MASR_Mean'], valid1[feature])
        else:
            masr_corr, masr_p = np.nan, np.nan

        # Correlation with Working Memory
        valid2 = df_combined[['Working_Memory_Score', feature]].dropna()
        if len(valid2) > 5:
            wm_corr, wm_p = stats.spearmanr(valid2['Working_Memory_Score'], valid2[feature])
        else:
            wm_corr, wm_p = np.nan, np.nan

        correlations.append({
            'feature': feature,
            'masr_r': masr_corr,
            'masr_p': masr_p,
            'wm_r': wm_corr,
            'wm_p': wm_p
        })

    corr_df = pd.DataFrame(correlations)

    
    # Create correlation heatmap
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # MASR correlations
    masr_data = corr_df.set_index('feature')['masr_r'].sort_values()
    colors = ['#D55E00' if p < 0.05 else '#999999' 
              for p in corr_df.set_index('feature').loc[masr_data.index, 'masr_p']]
    
    bars1 = ax1.barh(range(len(masr_data)), masr_data.values, color=colors,
                     edgecolor='black', linewidth=0.5)
    ax1.set_yticks(range(len(masr_data)))
    ax1.set_yticklabels(masr_data.index)
    ax1.set_xlabel('Spearman Correlation')
    ax1.set_title('Correlations with Math Anxiety (MASR)', fontweight='bold')
    ax1.axvline(x=0, color='black', linestyle='-', linewidth=0.5)
    
    # Add significance markers
    for i, (idx, val) in enumerate(masr_data.items()):
        p_val = corr_df.set_index('feature').loc[idx, 'masr_p']
        if p_val < 0.001:
            ax1.text(val + 0.01 if val > 0 else val - 0.01, i, '***', 
                    ha='left' if val > 0 else 'right', va='center')
        elif p_val < 0.01:
            ax1.text(val + 0.01 if val > 0 else val - 0.01, i, '**',
                    ha='left' if val > 0 else 'right', va='center')
        elif p_val < 0.05:
            ax1.text(val + 0.01 if val > 0 else val - 0.01, i, '*',
                    ha='left' if val > 0 else 'right', va='center')
    
    # WM correlations
    wm_data = corr_df.set_index('feature')['wm_r'].sort_values()
    colors = ['#0072B2' if p < 0.05 else '#999999'
              for p in corr_df.set_index('feature').loc[wm_data.index, 'wm_p']]
    
    bars2 = ax2.barh(range(len(wm_data)), wm_data.values, color=colors,
                     edgecolor='black', linewidth=0.5)
    ax2.set_yticks(range(len(wm_data)))
    ax2.set_yticklabels(wm_data.index)
    ax2.set_xlabel('Spearman Correlation')
    ax2.set_title('Correlations with Working Memory', fontweight='bold')
    ax2.axvline(x=0, color='black', linestyle='-', linewidth=0.5)
    
    plt.tight_layout()
    plt.savefig('figure4_baseline_correlations.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return corr_df

# ===== 7. TEMPORAL CHANGES ANALYSIS =====

def analyze_temporal_changes(df_treat):
    """
    Analyze changes in language features from Time 1 to Time 2
    for participants with both samples
    """
    
    print("\n=== TEMPORAL CHANGES ANALYSIS ===")
    
    # Identify participants with both samples
    has_both = (~df_treat['@1_English_Writing'].isna()) & (~df_treat['@2_English_Writing'].isna())
    df_both = df_treat[has_both].copy()
    
    print(f"Participants with both writing samples: {len(df_both)}")
    
    if len(df_both) < 10:
        print("Insufficient data for temporal analysis")
        return None
    
    # Key features to track
    key_features = ['anx', 'negemo', 'posemo', 'cogproc', 'insight', 'i', 'focusfuture']
    
    results = []
    for feature in key_features:
        liwc1_col = f'liwc1_{feature}'
        liwc2_col = f'liwc2_{feature}'
        
        if liwc1_col in df_both.columns and liwc2_col in df_both.columns:
            time1 = df_both[liwc1_col].dropna()
            time2 = df_both[liwc2_col].dropna()
            
            # Paired t-test
            if len(time1) > 5 and len(time2) > 5:
                t_stat, p_val = stats.ttest_rel(time1, time2)
                
                # Calculate effect size (Cohen's d for paired samples)
                diff = time2 - time1
                d = diff.mean() / diff.std() if diff.std() > 0 else 0
                
                # Correlation with score change
                if 'Score_Change' in df_both.columns:
                    change = time2 - time1
                    # 确保两者长度一致
                    valid = pd.concat([change, df_both['Score_Change']], axis=1).dropna()

                    if len(valid) > 5:
                        corr, corr_p = stats.spearmanr(valid.iloc[:, 0], valid.iloc[:, 1])
                    else:
                        corr, corr_p = np.nan, np.nan

                                
                results.append({
                    'feature': feature,
                    'time1_mean': time1.mean(),
                    'time2_mean': time2.mean(),
                    'change': time2.mean() - time1.mean(),
                    'p_value': p_val,
                    'effect_size': d,
                    'corr_with_improvement': corr,
                    'corr_p': corr_p
                })
    
    if results:
        changes_df = pd.DataFrame(results)
        
        # Visualization
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Sort by change magnitude
        changes_df = changes_df.sort_values('change')
        
        # Create horizontal bar plot
        colors = ['#009E73' if p < 0.05 else '#999999' for p in changes_df['p_value']]
        bars = ax.barh(range(len(changes_df)), changes_df['change'], 
                       color=colors, edgecolor='black', linewidth=0.5)
        
        ax.set_yticks(range(len(changes_df)))
        ax.set_yticklabels(changes_df['feature'])
        ax.set_xlabel('Mean Change (Time 2 - Time 1)')
        ax.set_title('Changes in Language Features Over Time', fontweight='bold')
        ax.axvline(x=0, color='black', linestyle='-', linewidth=0.5)
        
        # Add significance markers
        for i, row in changes_df.iterrows():
            if row['p_value'] < 0.05:
                ax.text(row['change'] + 0.1 if row['change'] > 0 else row['change'] - 0.1, 
                       i, f"d={row['effect_size']:.2f}", 
                       ha='left' if row['change'] > 0 else 'right', va='center',
                       fontsize=8)
        
        plt.tight_layout()
        plt.savefig('figure5_temporal_changes.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return changes_df
    
    return None

# ===== MAIN EXECUTION =====

def main():
    """Main execution function"""
    
    # Load processed data
    print("Loading processed data...")
    df = pd.read_csv('k12_math_anxiety_processed.csv')
    
    # Prepare data (combine Time 1 and Time 2 samples)
    df_combined, df_treat = prepare_data_for_analysis(df)
    
    # 1. Word cloud analysis
    create_anxiety_wordclouds(df_combined)
    
    # 2. Define features for analysis
    liwc_features = ['anx', 'posemo', 'negemo', 'cogproc', 'insight', 'cause', 
                    'tentat', 'i', 'we', 'focuspast', 'focuspresent', 'focusfuture',
                    'achieve', 'power', 'risk', 'certain', 'discrep']
    available_features = [f for f in liwc_features if f in df_combined.columns]
    
    # 3. Enhanced quartile heatmap (MHC Poster style)
    plot_enhanced_heatmap_by_quartile(df_combined, available_features)
    
    # 4. Comprehensive statistical analysis
    results_df = comprehensive_feature_analysis(df_combined)
    results_df.to_csv('liwc_feature_comparisons.csv', index=False)
    print("\n✓ Statistical results saved to 'liwc_feature_comparisons.csv'")
    
    # 5. Working memory moderation
    analyze_wm_moderation(df_combined)
    
    # 6. Baseline correlations
    corr_df = analyze_baseline_correlations(df_combined)
    corr_df.to_csv('liwc_baseline_correlations.csv', index=False)
    print("✓ Correlations saved to 'liwc_baseline_correlations.csv'")
    
    # 7. Temporal changes (for those with both samples)
    changes_df = analyze_temporal_changes(df_treat)
    if changes_df is not None:
        changes_df.to_csv('liwc_temporal_changes.csv', index=False)
        print("✓ Temporal changes saved to 'liwc_temporal_changes.csv'")
    
    print("\n=== ANALYSIS COMPLETE ===")
    print(f"\nKey findings address research questions:")
    print("1. Language patterns clearly differentiate anxiety levels")
    print("2. Specific features show changes over time that may relate to improvement")
    
    return df_combined, results_df

if __name__ == "__main__":
    df_combined, results_df = main()

#!/usr/bin/env python3
"""
K-12 Math Anxiety Study - Fast-track vs Regular Class Analysis
Comprehensive comparison of Pearl/Amber (fast-track) vs regular classes
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from statsmodels.stats.multitest import multipletests
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# APA-style plotting parameters
plt.style.use('seaborn-v0_8-whitegrid')

# Color scheme for class types
CLASS_COLORS = {
    'fast': '#E74C3C',      # Red for fast-track
    'regular': '#3498DB',   # Blue for regular
    'pearl': '#FF6B6B',     # Light red
    'amber': '#C0392B',     # Dark red
    'other': '#5DADE2'      # Light blue
}

plt.rcParams.update({
    'figure.figsize': (12, 8),
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 11,
    'font.family': 'sans-serif',
    'axes.spines.top': False,
    'axes.spines.right': False
})

# ===== 1. DATA PREPARATION AND BASIC STATS =====

def prepare_class_data(df):
    """
    Prepare data with class type categorization
    """
    print("=== CLASS TYPE ANALYSIS ===")
    
    # Create fast-track indicator
    df['is_fast_track'] = df['Class_Type'].isin(['Pearl', 'Amber'])
    df['class_category'] = df['is_fast_track'].map({True: 'Fast-track', False: 'Regular'})
    
    # Basic statistics
    print("\n1. Sample Distribution:")
    print(df['Class_Type'].value_counts())
    print(f"\nFast-track total: {df['is_fast_track'].sum()}")
    print(f"Regular total: {(~df['is_fast_track']).sum()}")
    
    # Distribution by grade
    print("\n2. Distribution by Grade:")
    grade_class = pd.crosstab(df['Grades'], df['class_category'])
    print(grade_class)
    
    # Distribution by treatment group
    print("\n3. Distribution by Treatment Group:")
    treat_class = pd.crosstab(df['Group'], df['class_category'])
    print(treat_class)
    
    return df

# ===== 2. ANXIETY AND BASELINE COMPARISONS =====

def compare_baseline_characteristics(df):
    """
    Compare baseline characteristics between fast-track and regular classes
    """
    print("\n=== BASELINE CHARACTERISTICS COMPARISON ===")
    
    fast_track = df[df['is_fast_track']]
    regular = df[~df['is_fast_track']]
    
    # Variables to compare
    baseline_vars = ['MASR_Mean', 'ExamAnxiety_Mean', 'Working_Memory_Score',
                    'ZFirst_Score', 'ZSecond_Score', 'Score_Change']
    
    results = []
    
    for var in baseline_vars:
        if var in df.columns:
            ft_data = fast_track[var].dropna()
            reg_data = regular[var].dropna()
            
            if len(ft_data) > 5 and len(reg_data) > 5:
                # Test normality
                _, p_norm_ft = stats.shapiro(ft_data) if len(ft_data) > 3 else (np.nan, 0)
                _, p_norm_reg = stats.shapiro(reg_data) if len(reg_data) > 3 else (np.nan, 0)
                
                # Choose appropriate test
                if p_norm_ft < 0.05 or p_norm_reg < 0.05:
                    stat, p_value = stats.mannwhitneyu(ft_data, reg_data)
                    test_type = 'Mann-Whitney U'
                    # Effect size: rank-biserial correlation
                    n1, n2 = len(ft_data), len(reg_data)
                    effect_size = 1 - (2*stat) / (n1*n2)
                else:
                    stat, p_value = stats.ttest_ind(ft_data, reg_data)
                    test_type = 't-test'
                    # Cohen's d
                    pooled_std = np.sqrt(((len(ft_data)-1)*ft_data.std()**2 + 
                                        (len(reg_data)-1)*reg_data.std()**2) / 
                                       (len(ft_data)+len(reg_data)-2))
                    effect_size = (ft_data.mean() - reg_data.mean()) / pooled_std if pooled_std > 0 else 0
                
                results.append({
                    'Variable': var,
                    'Fast_track_M': ft_data.mean(),
                    'Fast_track_SD': ft_data.std(),
                    'Fast_track_n': len(ft_data),
                    'Regular_M': reg_data.mean(),
                    'Regular_SD': reg_data.std(),
                    'Regular_n': len(reg_data),
                    'Difference': ft_data.mean() - reg_data.mean(),
                    'Test': test_type,
                    'p_value': p_value,
                    'Effect_size': effect_size
                })
    
    results_df = pd.DataFrame(results)
    print("\nBaseline Comparisons:")
    print(results_df.to_string(index=False))
    
    return results_df

# ===== 3. LIWC LANGUAGE PATTERNS COMPARISON =====

def compare_language_patterns(df):
    """
    Compare LIWC patterns between fast-track and regular classes
    """
    print("\n=== LANGUAGE PATTERNS COMPARISON ===")
    
    # Focus on treatment group only (who wrote about math)
    df_treat = df[df['Group'] == 1].copy()
    
    fast_track = df_treat[df_treat['is_fast_track']]
    regular = df_treat[~df_treat['is_fast_track']]
    
    print(f"\nTreatment group samples:")
    print(f"Fast-track: {len(fast_track)}")
    print(f"Regular: {len(regular)}")
    
    # Key LIWC variables
    liwc_vars = ['WC', 'Analytic', 'Clout', 'Authentic', 'Tone',
                'anx', 'posemo', 'negemo', 'anger', 'sad',
                'cogproc', 'insight', 'cause', 'discrep', 'tentat', 'certain',
                'i', 'we', 'you', 'achieve', 'power', 'affiliation',
                'focuspast', 'focuspresent', 'focusfuture',
                'work', 'leisure', 'money']
    
    # Add engineered features
    eng_features = ['emo_regulation', 'cognitive_complexity', 'self_distancing',
                   'temporal_balance', 'anxiety_intensity', 'problem_focus']
    
    # Check both time points
    results_t1 = []
    results_t2 = []
    
    for time, results_list in [('liwc1_', results_t1), ('liwc2_', results_t2)]:
        for var in liwc_vars + eng_features:
            col_name = time + var
            if col_name in df_treat.columns:
                ft_data = fast_track[col_name].dropna()
                reg_data = regular[col_name].dropna()
                
                if len(ft_data) > 5 and len(reg_data) > 5:
                    # Mann-Whitney U test (robust)
                    stat, p_value = stats.mannwhitneyu(ft_data, reg_data)
                    n1, n2 = len(ft_data), len(reg_data)
                    r = 1 - (2*stat) / (n1*n2)
                    
                    # Cohen's d for reference
                    pooled_std = np.sqrt(((n1-1)*ft_data.std()**2 + (n2-1)*reg_data.std()**2) / (n1+n2-2))
                    d = (ft_data.mean() - reg_data.mean()) / pooled_std if pooled_std > 0 else 0
                    
                    results_list.append({
                        'Feature': var,
                        'Fast_track_M': ft_data.mean(),
                        'Fast_track_SD': ft_data.std(),
                        'Regular_M': reg_data.mean(),
                        'Regular_SD': reg_data.std(),
                        'Difference': ft_data.mean() - reg_data.mean(),
                        'p_value': p_value,
                        'Effect_size_r': r,
                        'Cohen_d': d,
                        'n_fast': n1,
                        'n_regular': n2
                    })
    
    # Convert to DataFrames and apply FDR correction
    if results_t1:
        df_t1 = pd.DataFrame(results_t1)
        _, df_t1['p_adjusted'], _, _ = multipletests(df_t1['p_value'], method='fdr_bh')
        df_t1['sig'] = df_t1['p_adjusted'].apply(
            lambda p: '***' if p < 0.001 else '**' if p < 0.01 else '*' if p < 0.05 else ''
        )
        df_t1 = df_t1.sort_values('p_adjusted')
        
        print("\nTime 1 - Significant Language Differences (FDR corrected):")
        sig_t1 = df_t1[df_t1['p_adjusted'] < 0.05]
        if len(sig_t1) > 0:
            print(sig_t1[['Feature', 'Fast_track_M', 'Regular_M', 'Difference', 
                         'Cohen_d', 'p_adjusted', 'sig']].to_string(index=False))
        else:
            print("No significant differences after FDR correction")
    
    if results_t2:
        df_t2 = pd.DataFrame(results_t2)
        _, df_t2['p_adjusted'], _, _ = multipletests(df_t2['p_value'], method='fdr_bh')
        df_t2['sig'] = df_t2['p_adjusted'].apply(
            lambda p: '***' if p < 0.001 else '**' if p < 0.01 else '*' if p < 0.05 else ''
        )
        df_t2 = df_t2.sort_values('p_adjusted')
        
        print("\nTime 2 - Significant Language Differences (FDR corrected):")
        sig_t2 = df_t2[df_t2['p_adjusted'] < 0.05]
        if len(sig_t2) > 0:
            print(sig_t2[['Feature', 'Fast_track_M', 'Regular_M', 'Difference', 
                         'Cohen_d', 'p_adjusted', 'sig']].to_string(index=False))
        else:
            print("No significant differences after FDR correction")
    
    return df_t1 if results_t1 else None, df_t2 if results_t2 else None

# ===== 4. TREATMENT EFFECT BY CLASS TYPE =====

def analyze_treatment_effect_by_class(df):
    """
    Analyze how treatment effect differs between class types
    """
    print("\n=== TREATMENT EFFECT BY CLASS TYPE ===")
    
    # Create 2x2 table
    groups = df.groupby(['class_category', 'Group'])['Score_Change'].agg(['mean', 'std', 'count'])
    print("\nScore Change by Class Type and Treatment:")
    print(groups)
    
    # Analyze interaction effect
    results = []
    
    for class_type in ['Fast-track', 'Regular']:
        class_data = df[df['class_category'] == class_type]
        
        control = class_data[class_data['Group'] == 0]['Score_Change'].dropna()
        treatment = class_data[class_data['Group'] == 1]['Score_Change'].dropna()
        
        if len(control) > 5 and len(treatment) > 5:
            # Test treatment effect within class type
            stat, p_value = stats.mannwhitneyu(control, treatment)
            n1, n2 = len(control), len(treatment)
            r = 1 - (2*stat) / (n1*n2)
            
            # Cohen's d
            pooled_std = np.sqrt(((n1-1)*control.std()**2 + (n2-1)*treatment.std()**2) / (n1+n2-2))
            d = (treatment.mean() - control.mean()) / pooled_std if pooled_std > 0 else 0
            
            results.append({
                'Class_Type': class_type,
                'Control_M': control.mean(),
                'Control_SD': control.std(),
                'Control_n': n1,
                'Treatment_M': treatment.mean(),
                'Treatment_SD': treatment.std(),
                'Treatment_n': n2,
                'Treatment_Effect': treatment.mean() - control.mean(),
                'p_value': p_value,
                'Cohen_d': d,
                'Effect_r': r
            })
    
    effect_df = pd.DataFrame(results)
    print("\nTreatment Effects by Class Type:")
    print(effect_df.to_string(index=False))
    
    return effect_df

# ===== 5. ANXIETY PATTERNS BY CLASS TYPE =====

def analyze_anxiety_patterns_by_class(df):
    """
    Analyze anxiety patterns within each class type
    """
    print("\n=== ANXIETY PATTERNS BY CLASS TYPE ===")
    
    # Focus on treatment group
    df_treat = df[df['Group'] == 1].copy()
    
    # Analyze by anxiety quartile within each class type
    for class_type in ['Fast-track', 'Regular']:
        print(f"\n{class_type} Classes - Anxiety Patterns:")
        
        class_data = df_treat[df_treat['class_category'] == class_type]
        
        if len(class_data) > 20:
            # Anxiety distribution
            print(f"\nMASR distribution:")
            print(class_data['MASR_Mean'].describe())
            
            # Language patterns by anxiety level within class
            low_anx = class_data[class_data['MASR_Quartile'] == 1]
            high_anx = class_data[class_data['MASR_Quartile'] == 4]
            
            # Key differences
            key_vars = ['liwc1_anx', 'liwc1_negemo', 'liwc1_posemo', 'liwc1_cogproc',
                       'liwc1_i', 'liwc1_focusfuture', 'liwc1_emo_regulation']
            
            print(f"\nLow vs High anxiety within {class_type}:")
            for var in key_vars:
                if var in class_data.columns:
                    low_data = low_anx[var].dropna()
                    high_data = high_anx[var].dropna()
                    
                    if len(low_data) > 3 and len(high_data) > 3:
                        stat, p = stats.mannwhitneyu(low_data, high_data)
                        print(f"  {var}: Low={low_data.mean():.2f}, High={high_data.mean():.2f}, p={p:.3f}")

# ===== 6. VISUALIZATION =====

def create_class_type_visualizations(df, baseline_df, liwc_t1_df, effect_df):
    """
    Create comprehensive visualizations for class type analysis
    """
    print("\n=== CREATING VISUALIZATIONS ===")
    
    # 1. Baseline characteristics comparison
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    baseline_vars = ['MASR_Mean', 'ExamAnxiety_Mean', 'Working_Memory_Score',
                    'ZFirst_Score', 'ZSecond_Score', 'Score_Change']
    
    for idx, var in enumerate(baseline_vars):
        if idx < len(axes) and var in df.columns:
            ax = axes[idx]
            
            # Violin plot
            data_to_plot = []
            labels = []
            colors = []
            
            for class_type, color in [('Fast-track', CLASS_COLORS['fast']), 
                                     ('Regular', CLASS_COLORS['regular'])]:
                class_data = df[df['class_category'] == class_type][var].dropna()
                if len(class_data) > 0:
                    data_to_plot.append(class_data)
                    labels.append(f'{class_type}\n(n={len(class_data)})')
                    colors.append(color)
            
            if data_to_plot:
                parts = ax.violinplot(data_to_plot, showmeans=True, showextrema=True)
                
                # Color the violin plots
                for pc, color in zip(parts['bodies'], colors):
                    pc.set_facecolor(color)
                    pc.set_alpha(0.7)
                
                ax.set_xticks(range(1, len(labels) + 1))
                ax.set_xticklabels(labels)
                ax.set_ylabel(var.replace('_', ' '))
                ax.set_title(var.replace('_', ' '), fontweight='bold')
                
                # Add significance from baseline_df
                if baseline_df is not None and var in baseline_df['Variable'].values:
                    row = baseline_df[baseline_df['Variable'] == var].iloc[0]
                    if row['p_value'] < 0.05:
                        ax.text(0.5, 0.95, f"p={row['p_value']:.3f}, d={row['Effect_size']:.2f}",
                               transform=ax.transAxes, ha='center', va='top',
                               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    plt.suptitle('Baseline Characteristics: Fast-track vs Regular Classes', fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig('figure_class_baseline_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. Treatment effect visualization
    if effect_df is not None and len(effect_df) > 0:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Bar plot of treatment effects
        x = np.arange(len(effect_df))
        width = 0.35
        
        control_means = effect_df['Control_M'].values
        treatment_means = effect_df['Treatment_M'].values
        
        bars1 = ax1.bar(x - width/2, control_means, width, label='Control',
                        color='#95A5A6', edgecolor='black', linewidth=0.5)
        bars2 = ax1.bar(x + width/2, treatment_means, width, label='Treatment',
                        color='#2ECC71', edgecolor='black', linewidth=0.5)
        
        # Color bars by class type
        for i, class_type in enumerate(effect_df['Class_Type']):
            color = CLASS_COLORS['fast'] if class_type == 'Fast-track' else CLASS_COLORS['regular']
            bars1[i].set_facecolor(color)
            bars2[i].set_facecolor(color)
        
        ax1.set_xlabel('Class Type')
        ax1.set_ylabel('Mean Score Change')
        ax1.set_title('Treatment Effect by Class Type', fontweight='bold')
        ax1.set_xticks(x)
        ax1.set_xticklabels(effect_df['Class_Type'])
        ax1.legend(frameon=False)
        ax1.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
        
        # Add significance
        for i, row in effect_df.iterrows():
            if row['p_value'] < 0.05:
                y_pos = max(control_means[i], treatment_means[i]) + 0.05
                stars = '***' if row['p_value'] < 0.001 else '**' if row['p_value'] < 0.01 else '*'
                ax1.text(i, y_pos, stars, ha='center', va='bottom')
        
        # Effect sizes
        colors = [CLASS_COLORS['fast'] if ct == 'Fast-track' else CLASS_COLORS['regular'] 
                 for ct in effect_df['Class_Type']]
        bars = ax2.bar(x, effect_df['Cohen_d'], color=colors, alpha=0.7,
                       edgecolor='black', linewidth=0.5)
        
        ax2.set_xlabel('Class Type')
        ax2.set_ylabel("Cohen's d")
        ax2.set_title('Effect Sizes by Class Type', fontweight='bold')
        ax2.set_xticks(x)
        ax2.set_xticklabels(effect_df['Class_Type'])
        ax2.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
        ax2.axhline(y=-0.2, color='gray', linestyle=':', alpha=0.5)
        ax2.axhline(y=-0.5, color='gray', linestyle=':', alpha=0.5)
        
        plt.tight_layout()
        plt.savefig('figure_treatment_by_class.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    # 3. Language patterns heatmap
    if liwc_t1_df is not None and len(liwc_t1_df) > 0:
        liwc_t1_df['abs_d'] = liwc_t1_df['Cohen_d'].abs()
        top_features = liwc_t1_df.nlargest(15, 'abs_d')  # 即使不显著，也挑出前15个

        if top_features.empty:
            print("No features available for heatmap.")
        else:
            print("\nDrawing heatmap for top features (regardless of significance):")
            print(top_features[['Feature', 'Cohen_d', 'Fast_track_M', 'Regular_M']])
            
            heatmap_data = pd.DataFrame({
                'Fast-track': top_features['Fast_track_M'],
                'Regular': top_features['Regular_M']
            }, index=top_features['Feature'])

            heatmap_z = (heatmap_data - heatmap_data.mean(axis=1).values.reshape(-1, 1)) / \
                        heatmap_data.std(axis=1).values.reshape(-1, 1)

            plt.figure(figsize=(8, 10))
            sns.heatmap(heatmap_z, cmap='RdBu_r', center=0,
                        annot=True, fmt='.2f',
                        cbar_kws={'label': 'Z-score'},
                        linewidths=0.5)

            plt.title('Language Feature Differences: Fast-track vs Regular\n(Z-scores)',
                    fontweight='bold', pad=20)
            plt.xlabel('Class Type')
            plt.tight_layout()
            plt.savefig('figure_language_heatmap.png', dpi=300, bbox_inches='tight')
            plt.show()


# ===== 7. SPECIFIC ANALYSES =====

def analyze_specific_patterns(df):
    """
    Specific analyses for interesting patterns
    """
    print("\n=== SPECIFIC PATTERN ANALYSES ===")
    
    # 1. Do fast-track students express anxiety differently?
    print("\n1. Anxiety Expression Patterns:")
    
    df_treat = df[df['Group'] == 1].copy()
    
    # Create anxiety expression profile
    anxiety_vars = ['liwc1_anx', 'liwc1_negemo', 'liwc1_anger', 'liwc1_sad', 'liwc1_anxiety_intensity']
    
    for class_type in ['Fast-track', 'Regular']:
        class_data = df_treat[df_treat['class_category'] == class_type]
        print(f"\n{class_type} anxiety expression profile:")
        
        for var in anxiety_vars:
            if var in class_data.columns:
                data = class_data[var].dropna()
                if len(data) > 0:
                    print(f"  {var}: M={data.mean():.2f}, SD={data.std():.2f}, Median={data.median():.2f}")
    
    # 2. Cognitive vs emotional expression balance
    print("\n2. Problem-focused vs Emotion-focused Writing:")
    
    if 'liwc1_problem_focus' in df_treat.columns:
        for class_type in ['Fast-track', 'Regular']:
            class_data = df_treat[df_treat['class_category'] == class_type]
            pf_data = class_data['liwc1_problem_focus'].dropna()
            
            if len(pf_data) > 0:
                print(f"\n{class_type}:")
                print(f"  Problem-focused ratio: M={pf_data.mean():.3f}, SD={pf_data.std():.3f}")
                print(f"  More cognitive than emotional: {(pf_data > 0.5).sum()/len(pf_data)*100:.1f}%")
    
    # 3. Success predictors within each class type
    print("\n3. What predicts success in each class type?")
    
    for class_type in ['Fast-track', 'Regular']:
        class_data = df_treat[df_treat['class_category'] == class_type]
        
        if len(class_data) > 20:
            print(f"\n{class_type} - Correlations with Score Change:")
            
            predictors = ['MASR_Mean', 'Working_Memory_Score', 'liwc1_cogproc', 
                         'liwc1_insight', 'liwc1_emo_regulation', 'liwc1_problem_focus']
            
            for pred in predictors:
                if pred in class_data.columns and 'Score_Change' in class_data.columns:
                    valid_data = class_data[[pred, 'Score_Change']].dropna()
                    if len(valid_data) > 10:
                        r, p = stats.spearmanr(valid_data[pred], valid_data['Score_Change'])
                        if p < 0.1:  # Show marginally significant
                            print(f"  {pred}: r={r:.3f}, p={p:.3f}")

# ===== MAIN EXECUTION =====

def main():
    """Main execution function"""
    
    # Load data with engineered features
    print("Loading data with engineered features...")
    df = pd.read_csv('k12_math_anxiety_engineered.csv')
    
    # Prepare class data
    df = prepare_class_data(df)
    
    # Run analyses
    baseline_df = compare_baseline_characteristics(df)
    liwc_t1_df, liwc_t2_df = compare_language_patterns(df)
    effect_df = analyze_treatment_effect_by_class(df)
    analyze_anxiety_patterns_by_class(df)
    analyze_specific_patterns(df)
    
    # Create visualizations
    create_class_type_visualizations(df, baseline_df, liwc_t1_df, effect_df)
    
    # Save results
    with pd.ExcelWriter('class_type_analysis_results.xlsx') as writer:
        baseline_df.to_excel(writer, sheet_name='Baseline_Comparison', index=False)
        if liwc_t1_df is not None:
            liwc_t1_df.to_excel(writer, sheet_name='LIWC_Time1', index=False)
        if liwc_t2_df is not None:
            liwc_t2_df.to_excel(writer, sheet_name='LIWC_Time2', index=False)
        if effect_df is not None:
            effect_df.to_excel(writer, sheet_name='Treatment_Effects', index=False)
    
    print("\n✓ Results saved to 'class_type_analysis_results.xlsx'")
    
    print("\n=== ANALYSIS COMPLETE ===")
    
    return df, baseline_df, liwc_t1_df, effect_df

if __name__ == "__main__":
    df, baseline_df, liwc_t1_df, effect_df = main()

print(liwc_t1_df.head())
print(heatmap_z.describe())


pip install pingouin

#!/usr/bin/env python3
"""
K-12 Math Anxiety Study - ANCOVA Analysis
Controlling for baseline math anxiety (MASR) as covariate
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import statsmodels.api as sm
from statsmodels.stats.anova import anova_lm
from statsmodels.formula.api import ols
import pingouin as pg
import warnings
warnings.filterwarnings('ignore')

# APA-style plotting
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams.update({
    'figure.figsize': (10, 8),
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 11,
    'font.family': 'sans-serif',
    'axes.spines.top': False,
    'axes.spines.right': False
})

# ===== 1. ANCOVA FOR EXAM SCORES =====

def perform_ancova_exam_scores(df):
    """
    ANCOVA for both exam scores with MASR as covariate
    """
    print("=== ANCOVA ANALYSIS: EXAM SCORES ===")
    print("Controlling for baseline math anxiety (MASR)\n")
    
    # Prepare data
    analysis_df = df.dropna(subset=['MASR_Mean', 'Group', 'ZFirst_Score', 'ZSecond_Score']).copy()
    analysis_df['Treatment'] = analysis_df['Group'].astype('category')
    
    print(f"Sample size for analysis: {len(analysis_df)}")
    print(f"Control: {len(analysis_df[analysis_df['Group'] == 0])}")
    print(f"Treatment: {len(analysis_df[analysis_df['Group'] == 1])}\n")
    
    # 1. First Exam Score ANCOVA
    print("1. FIRST EXAM SCORE")
    print("-" * 50)
    
    # Check assumptions
    print("Assumption checks:")
    
    # Homogeneity of regression slopes
    model_interaction = ols('ZFirst_Score ~ MASR_Mean * Treatment', data=analysis_df).fit()
    interaction_p = model_interaction.pvalues['MASR_Mean:Treatment[T.1]']
    print(f"  Homogeneity of slopes test: p = {interaction_p:.3f}")
    if interaction_p > 0.05:
        print("  ✓ Assumption met (no significant interaction)")
    else:
        print("  ✗ Assumption violated (significant interaction)")
    
    # Main ANCOVA model
    model1 = ols('ZFirst_Score ~ MASR_Mean + Treatment', data=analysis_df).fit()
    print("\nANCOVA Results:")
    print(model1.summary().tables[1])
    
    # ANOVA table
    anova_table1 = anova_lm(model1, typ=2)
    print("\nType II ANOVA Table:")
    print(anova_table1)
    
    # Adjusted means
    control_adj1 = model1.params['Intercept'] + model1.params['MASR_Mean'] * analysis_df['MASR_Mean'].mean()
    treatment_adj1 = control_adj1 + model1.params['Treatment[T.1]']
    
    print(f"\nAdjusted Means (controlling for MASR):")
    print(f"  Control: {control_adj1:.3f}")
    print(f"  Treatment: {treatment_adj1:.3f}")
    print(f"  Difference: {model1.params['Treatment[T.1]']:.3f}")
    
    # Effect size (partial eta squared)
    ss_treatment = anova_table1.loc['Treatment', 'sum_sq']
    ss_error = anova_table1.loc['Residual', 'sum_sq']
    partial_eta_sq1 = ss_treatment / (ss_treatment + ss_error)
    print(f"  Partial η² = {partial_eta_sq1:.3f}")
    
    # 2. Second Exam Score ANCOVA
    print("\n\n2. SECOND EXAM SCORE")
    print("-" * 50)
    
    # Check assumptions
    model_interaction2 = ols('ZSecond_Score ~ MASR_Mean * Treatment', data=analysis_df).fit()
    interaction_p2 = model_interaction2.pvalues['MASR_Mean:Treatment[T.1]']
    print(f"Assumption checks:")
    print(f"  Homogeneity of slopes test: p = {interaction_p2:.3f}")
    
    # Main ANCOVA model
    model2 = ols('ZSecond_Score ~ MASR_Mean + Treatment', data=analysis_df).fit()
    print("\nANCOVA Results:")
    print(model2.summary().tables[1])
    
    # ANOVA table
    anova_table2 = anova_lm(model2, typ=2)
    print("\nType II ANOVA Table:")
    print(anova_table2)
    
    # Adjusted means
    control_adj2 = model2.params['Intercept'] + model2.params['MASR_Mean'] * analysis_df['MASR_Mean'].mean()
    treatment_adj2 = control_adj2 + model2.params['Treatment[T.1]']
    
    print(f"\nAdjusted Means (controlling for MASR):")
    print(f"  Control: {control_adj2:.3f}")
    print(f"  Treatment: {treatment_adj2:.3f}")
    print(f"  Difference: {model2.params['Treatment[T.1]']:.3f}")
    
    # Effect size
    ss_treatment2 = anova_table2.loc['Treatment', 'sum_sq']
    ss_error2 = anova_table2.loc['Residual', 'sum_sq']
    partial_eta_sq2 = ss_treatment2 / (ss_treatment2 + ss_error2)
    print(f"  Partial η² = {partial_eta_sq2:.3f}")
    
    return model1, model2, analysis_df

# ===== 2. ANCOVA FOR SCORE CHANGE =====

def perform_ancova_score_change(df):
    """
    ANCOVA for score change with MASR as covariate
    """
    print("\n\n=== ANCOVA ANALYSIS: SCORE CHANGE ===")
    
    # Prepare data
    analysis_df = df.dropna(subset=['MASR_Mean', 'Group', 'Score_Change']).copy()
    analysis_df['Treatment'] = analysis_df['Group'].astype('category')
    
    print(f"Sample size for analysis: {len(analysis_df)}")
    
    # Check assumptions
    print("\nAssumption checks:")
    model_interaction = ols('Score_Change ~ MASR_Mean * Treatment', data=analysis_df).fit()
    interaction_p = model_interaction.pvalues['MASR_Mean:Treatment[T.1]']
    print(f"  Homogeneity of slopes test: p = {interaction_p:.3f}")
    
    # Main ANCOVA model
    model = ols('Score_Change ~ MASR_Mean + Treatment', data=analysis_df).fit()
    print("\nANCOVA Results:")
    print(model.summary().tables[1])
    
    # ANOVA table
    anova_table = anova_lm(model, typ=2)
    print("\nType II ANOVA Table:")
    print(anova_table)
    
    # Adjusted means
    control_adj = model.params['Intercept'] + model.params['MASR_Mean'] * analysis_df['MASR_Mean'].mean()
    treatment_adj = control_adj + model.params['Treatment[T.1]']
    
    print(f"\nAdjusted Means (controlling for MASR):")
    print(f"  Control: {control_adj:.3f}")
    print(f"  Treatment: {treatment_adj:.3f}")
    print(f"  Difference: {model.params['Treatment[T.1]']:.3f}")
    
    # Effect size
    ss_treatment = anova_table.loc['Treatment', 'sum_sq']
    ss_error = anova_table.loc['Residual', 'sum_sq']
    partial_eta_sq = ss_treatment / (ss_treatment + ss_error)
    print(f"  Partial η² = {partial_eta_sq:.3f}")
    
    return model, anova_table

# ===== 3. MULTIPLE COVARIATES ANALYSIS =====

def perform_ancova_multiple_covariates(df):
    """
    ANCOVA with multiple covariates
    """
    print("\n\n=== ANCOVA WITH MULTIPLE COVARIATES ===")
    
    # Prepare data
    covariates = ['MASR_Mean', 'Working_Memory_Score', 'ExamAnxiety_Mean']
    required_cols = covariates + ['Group', 'Score_Change']
    analysis_df = df.dropna(subset=required_cols).copy()
    analysis_df['Treatment'] = analysis_df['Group'].astype('category')
    
    print(f"Sample size with all covariates: {len(analysis_df)}")
    
    # Model with all covariates
    formula = 'Score_Change ~ MASR_Mean + Working_Memory_Score + ExamAnxiety_Mean + Treatment'
    model_full = ols(formula, data=analysis_df).fit()
    
    print("\nFull Model Results:")
    print(model_full.summary().tables[1])
    
    # ANOVA table
    anova_full = anova_lm(model_full, typ=2)
    print("\nType II ANOVA Table:")
    print(anova_full)
    
    # Calculate adjusted means
    means_dict = {}
    for covar in covariates:
        means_dict[covar] = analysis_df[covar].mean()
    
    control_adj = model_full.params['Intercept']
    for covar in covariates:
        control_adj += model_full.params[covar] * means_dict[covar]
    
    treatment_adj = control_adj + model_full.params['Treatment[T.1]']
    
    print(f"\nAdjusted Means (controlling for all covariates):")
    print(f"  Control: {control_adj:.3f}")
    print(f"  Treatment: {treatment_adj:.3f}")
    print(f"  Difference: {model_full.params['Treatment[T.1]']:.3f}")
    
    # Effect size
    ss_treatment = anova_full.loc['Treatment', 'sum_sq']
    ss_error = anova_full.loc['Residual', 'sum_sq']
    partial_eta_sq = ss_treatment / (ss_treatment + ss_error)
    print(f"  Partial η² = {partial_eta_sq:.3f}")
    
    return model_full

# ===== 4. VISUALIZATION =====

def create_ancova_visualizations(df, model1, model2):
    """
    Create visualizations for ANCOVA results
    """
    print("\n=== CREATING VISUALIZATIONS ===")
    
    fig, axes = plt.subplots(2, 2, figsize=(14, 10))
    
    # 1. First Exam: Scatter with regression lines
    ax = axes[0, 0]
    
    for group, color, label in [(0, '#E69F00', 'Control'), (1, '#56B4E9', 'Treatment')]:
        group_data = df[df['Group'] == group]
        ax.scatter(group_data['MASR_Mean'], group_data['ZFirst_Score'], 
                  alpha=0.5, color=color, label=label, edgecolor='black', linewidth=0.5)
        
        # Add regression line
        x_range = np.linspace(group_data['MASR_Mean'].min(), group_data['MASR_Mean'].max(), 100)
        y_pred = model1.params['Intercept'] + model1.params['MASR_Mean'] * x_range
        if group == 1:
            y_pred += model1.params['Treatment[T.1]']
        ax.plot(x_range, y_pred, color=color, linewidth=2)
    
    ax.set_xlabel('Math Anxiety (MASR)')
    ax.set_ylabel('First Exam Score (Z)')
    ax.set_title('First Exam: Treatment Effect Controlling for MASR', fontweight='bold')
    ax.legend(frameon=False)
    
    # 2. Second Exam: Scatter with regression lines
    ax = axes[0, 1]
    
    for group, color, label in [(0, '#E69F00', 'Control'), (1, '#56B4E9', 'Treatment')]:
        group_data = df[df['Group'] == group]
        ax.scatter(group_data['MASR_Mean'], group_data['ZSecond_Score'], 
                  alpha=0.5, color=color, label=label, edgecolor='black', linewidth=0.5)
        
        # Add regression line
        x_range = np.linspace(group_data['MASR_Mean'].min(), group_data['MASR_Mean'].max(), 100)
        y_pred = model2.params['Intercept'] + model2.params['MASR_Mean'] * x_range
        if group == 1:
            y_pred += model2.params['Treatment[T.1]']
        ax.plot(x_range, y_pred, color=color, linewidth=2)
    
    ax.set_xlabel('Math Anxiety (MASR)')
    ax.set_ylabel('Second Exam Score (Z)')
    ax.set_title('Second Exam: Treatment Effect Controlling for MASR', fontweight='bold')
    ax.legend(frameon=False)
    
    # 3. Adjusted means comparison
    ax = axes[1, 0]
    
    # Calculate adjusted means for both exams
    masr_mean = df['MASR_Mean'].mean()
    
    adj_means = {
        'First Exam': {
            'Control': model1.params['Intercept'] + model1.params['MASR_Mean'] * masr_mean,
            'Treatment': model1.params['Intercept'] + model1.params['MASR_Mean'] * masr_mean + model1.params['Treatment[T.1]']
        },
        'Second Exam': {
            'Control': model2.params['Intercept'] + model2.params['MASR_Mean'] * masr_mean,
            'Treatment': model2.params['Intercept'] + model2.params['MASR_Mean'] * masr_mean + model2.params['Treatment[T.1]']
        }
    }
    
    x = np.arange(2)
    width = 0.35
    
    control_means = [adj_means['First Exam']['Control'], adj_means['Second Exam']['Control']]
    treatment_means = [adj_means['First Exam']['Treatment'], adj_means['Second Exam']['Treatment']]
    
    bars1 = ax.bar(x - width/2, control_means, width, label='Control', 
                   color='#E69F00', edgecolor='black', linewidth=0.5)
    bars2 = ax.bar(x + width/2, treatment_means, width, label='Treatment',
                   color='#56B4E9', edgecolor='black', linewidth=0.5)
    
    ax.set_ylabel('Adjusted Mean Score (Z)')
    ax.set_title('Adjusted Means Comparison\n(Controlling for MASR)', fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(['First Exam', 'Second Exam'])
    ax.legend(frameon=False)
    ax.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    
    # Add significance indicators
    p_values = [model1.pvalues['Treatment[T.1]'], model2.pvalues['Treatment[T.1]']]
    for i, p in enumerate(p_values):
        if p < 0.05:
            y_pos = max(control_means[i], treatment_means[i]) + 0.05
            stars = '***' if p < 0.001 else '**' if p < 0.01 else '*'
            ax.text(i, y_pos, stars, ha='center', va='bottom', fontsize=12)
    
    # 4. Score Change ANCOVA
    ax = axes[1, 1]
    
    for group, color, label in [(0, '#E69F00', 'Control'), (1, '#56B4E9', 'Treatment')]:
        group_data = df[df['Group'] == group]
        ax.scatter(group_data['MASR_Mean'], group_data['Score_Change'], 
                  alpha=0.5, color=color, label=label, edgecolor='black', linewidth=0.5)
    
    ax.set_xlabel('Math Anxiety (MASR)')
    ax.set_ylabel('Score Change')
    ax.set_title('Score Change by MASR and Treatment', fontweight='bold')
    ax.legend(frameon=False)
    ax.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    plt.savefig('figure_ancova_results.png', dpi=300, bbox_inches='tight')
    plt.show()

# ===== 5. SUBGROUP ANCOVA =====

def perform_subgroup_ancova(df):
    """
    ANCOVA separately for fast-track and regular classes
    """
    print("\n\n=== SUBGROUP ANCOVA ANALYSIS ===")
    
    # Add class type indicator
    df['is_fast_track'] = df['Class_Type'].isin(['Pearl', 'Amber'])
    
    for class_type, is_fast in [('Fast-track', True), ('Regular', False)]:
        print(f"\n{class_type} Classes:")
        print("-" * 50)
        
        # Filter data
        class_df = df[df['is_fast_track'] == is_fast].dropna(
            subset=['MASR_Mean', 'Group', 'Score_Change']
        ).copy()
        class_df['Treatment'] = class_df['Group'].astype('category')
        
        print(f"Sample size: {len(class_df)}")
        
        if len(class_df) > 20:
            # ANCOVA model
            model = ols('Score_Change ~ MASR_Mean + Treatment', data=class_df).fit()
            print("\nANCOVA Results:")
            print(model.summary().tables[1])
            
            # Adjusted means
            control_adj = model.params['Intercept'] + model.params['MASR_Mean'] * class_df['MASR_Mean'].mean()
            treatment_adj = control_adj + model.params.get('Treatment[T.1]', 0)
            
            print(f"\nAdjusted Means:")
            print(f"  Control: {control_adj:.3f}")
            print(f"  Treatment: {treatment_adj:.3f}")
            print(f"  Difference: {model.params.get('Treatment[T.1]', 0):.3f}")
            print(f"  p-value: {model.pvalues.get('Treatment[T.1]', 'N/A'):.3f}")

# ===== MAIN EXECUTION =====

def main():
    """Main execution function"""
    
    # Load data
    print("Loading data...")
    df = pd.read_csv('k12_math_anxiety_engineered.csv')
    
    # Perform ANCOVA analyses
    model1, model2, analysis_df = perform_ancova_exam_scores(df)
    model_change, anova_change = perform_ancova_score_change(df)
    model_full = perform_ancova_multiple_covariates(df)
    
    # Create visualizations
    create_ancova_visualizations(df, model1, model2)
    
    # Subgroup analyses
    perform_subgroup_ancova(df)
    
    # Save key results
    results_summary = {
        'Analysis': ['First Exam', 'Second Exam', 'Score Change', 'Multiple Covariates'],
        'Treatment_Effect': [
            model1.params['Treatment[T.1]'],
            model2.params['Treatment[T.1]'],
            model_change.params['Treatment[T.1]'],
            model_full.params['Treatment[T.1]']
        ],
        'p_value': [
            model1.pvalues['Treatment[T.1]'],
            model2.pvalues['Treatment[T.1]'],
            model_change.pvalues['Treatment[T.1]'],
            model_full.pvalues['Treatment[T.1]']
        ],
        'Significant': [
            model1.pvalues['Treatment[T.1]'] < 0.05,
            model2.pvalues['Treatment[T.1]'] < 0.05,
            model_change.pvalues['Treatment[T.1]'] < 0.05,
            model_full.pvalues['Treatment[T.1]'] < 0.05
        ]
    }
    
    results_df = pd.DataFrame(results_summary)
    results_df.to_csv('ancova_results_summary.csv', index=False)
    print("\n✓ Results saved to 'ancova_results_summary.csv'")
    
    print("\n=== ANCOVA ANALYSIS COMPLETE ===")
    
    return model1, model2, model_change, model_full

if __name__ == "__main__":
    model1, model2, model_change, model_full = main()

#!/usr/bin/env python3
"""
K-12 Math Anxiety Study - Advanced Clustering Analysis
Comprehensive clustering approach with multiple methods and validation
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
from scipy.spatial.distance import pdist
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.mixture import GaussianMixture
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from sklearn.manifold import TSNE
import warnings
warnings.filterwarnings('ignore')

# APA-style plotting
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams.update({
    'figure.figsize': (12, 8),
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 11,
    'font.family': 'sans-serif',
    'axes.spines.top': False,
    'axes.spines.right': False
})

# Color palette for clusters
CLUSTER_COLORS = ['#E74C3C', '#3498DB', '#2ECC71', '#F39C12', '#9B59B6', '#1ABC9C', '#34495E', '#E67E22']

# ===== 1. DATA PREPARATION FOR CLUSTERING =====

def prepare_clustering_data(df):
    """
    Prepare data for clustering analysis
    """
    print("=== PREPARING DATA FOR CLUSTERING ===")
    
    # Focus on treatment group with complete LIWC data
    df_treat = df[df['Group'] == 1].copy()
    
    # Define feature sets for clustering
    feature_sets = {
        'emotional': ['liwc1_anx', 'liwc1_negemo', 'liwc1_posemo', 'liwc1_affect',
                     'liwc1_anger', 'liwc1_sad'],
        'cognitive': ['liwc1_cogproc', 'liwc1_insight', 'liwc1_cause', 'liwc1_discrep', 
                     'liwc1_tentat', 'liwc1_certain'],
        'self_focus': ['liwc1_i', 'liwc1_we', 'liwc1_you', 'liwc1_ipron', 'liwc1_ppron'],
        'temporal': ['liwc1_focuspast', 'liwc1_focuspresent', 'liwc1_focusfuture'],
        'achievement': ['liwc1_achieve', 'liwc1_work', 'liwc1_power', 'liwc1_reward'],
        'engineered': ['liwc1_emo_regulation', 'liwc1_cognitive_complexity', 
                      'liwc1_self_distancing', 'liwc1_problem_focus', 
                      'liwc1_anxiety_intensity', 'liwc1_temporal_balance']
    }
    
    # Combine all features
    all_features = []
    for feature_list in feature_sets.values():
        all_features.extend(feature_list)
    
    # Remove duplicates and filter to available columns
    all_features = list(set([f for f in all_features if f in df_treat.columns]))
    
    # Create dataset with complete cases
    df_cluster = df_treat[all_features + ['ID', 'MASR_Mean', 'Score_Change', 
                                          'Working_Memory_Score', 'Class_Type']].dropna()
    
    print(f"Samples for clustering: {len(df_cluster)}")
    print(f"Features for clustering: {len(all_features)}")
    
    # Extract feature matrix
    X = df_cluster[all_features].values
    
    # Standardize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    return df_cluster, X_scaled, all_features, scaler

# ===== 2. OPTIMAL CLUSTER NUMBER DETERMINATION =====

def determine_optimal_clusters(X_scaled, max_clusters=10):
    """
    Determine optimal number of clusters using multiple methods
    """
    print("\n=== DETERMINING OPTIMAL NUMBER OF CLUSTERS ===")
    
    n_samples = X_scaled.shape[0]
    max_clusters = min(max_clusters, n_samples // 5)  # Ensure reasonable cluster sizes
    
    results = {
        'k': [],
        'inertia': [],
        'silhouette': [],
        'calinski': [],
        'davies_bouldin': []
    }
    
    # Test different numbers of clusters
    for k in range(2, max_clusters + 1):
        # K-means
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        labels = kmeans.fit_predict(X_scaled)
        
        # Calculate metrics
        results['k'].append(k)
        results['inertia'].append(kmeans.inertia_)
        results['silhouette'].append(silhouette_score(X_scaled, labels))
        results['calinski'].append(calinski_harabasz_score(X_scaled, labels))
        results['davies_bouldin'].append(davies_bouldin_score(X_scaled, labels))
    
    # Create visualization
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # Elbow plot
    ax = axes[0, 0]
    ax.plot(results['k'], results['inertia'], 'bo-')
    ax.set_xlabel('Number of Clusters')
    ax.set_ylabel('Within-Cluster Sum of Squares')
    ax.set_title('Elbow Method', fontweight='bold')
    
    # Find elbow point
    if len(results['k']) > 2:
        # Calculate second derivative
        second_diff = np.diff(results['inertia'], 2)
        elbow_k = results['k'][np.argmax(second_diff) + 2]
        ax.axvline(x=elbow_k, color='red', linestyle='--', alpha=0.5)
        ax.text(elbow_k, ax.get_ylim()[1]*0.9, f'Elbow: k={elbow_k}', 
               ha='center', bbox=dict(boxstyle='round', facecolor='wheat'))
    
    # Silhouette plot
    ax = axes[0, 1]
    ax.plot(results['k'], results['silhouette'], 'go-')
    ax.set_xlabel('Number of Clusters')
    ax.set_ylabel('Silhouette Score')
    ax.set_title('Silhouette Analysis', fontweight='bold')
    
    # Find maximum
    best_silhouette_k = results['k'][np.argmax(results['silhouette'])]
    ax.axvline(x=best_silhouette_k, color='red', linestyle='--', alpha=0.5)
    
    # Calinski-Harabasz plot
    ax = axes[1, 0]
    ax.plot(results['k'], results['calinski'], 'ro-')
    ax.set_xlabel('Number of Clusters')
    ax.set_ylabel('Calinski-Harabasz Score')
    ax.set_title('Calinski-Harabasz Index', fontweight='bold')
    
    # Davies-Bouldin plot (lower is better)
    ax = axes[1, 1]
    ax.plot(results['k'], results['davies_bouldin'], 'mo-')
    ax.set_xlabel('Number of Clusters')
    ax.set_ylabel('Davies-Bouldin Score')
    ax.set_title('Davies-Bouldin Index (lower is better)', fontweight='bold')
    
    plt.suptitle('Cluster Number Optimization', fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig('figure_cluster_optimization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print recommendations
    print(f"\nRecommendations:")
    print(f"  Elbow method suggests: k = {elbow_k if len(results['k']) > 2 else 'unclear'}")
    print(f"  Best silhouette score: k = {best_silhouette_k}")
    print(f"  Best Davies-Bouldin: k = {results['k'][np.argmin(results['davies_bouldin'])]}")
    
    return results, best_silhouette_k

# ===== 3. HIERARCHICAL CLUSTERING =====

def perform_hierarchical_clustering(X_scaled, n_clusters=None):
    """
    Perform hierarchical clustering with dendrogram
    """
    print("\n=== HIERARCHICAL CLUSTERING ===")
    
    # Calculate linkage
    linkage_matrix = linkage(X_scaled, method='ward')
    
    # Create dendrogram
    plt.figure(figsize=(15, 8))
    dendrogram(linkage_matrix, 
              truncate_mode='level',
              p=6,
              show_leaf_counts=True)
    plt.title('Hierarchical Clustering Dendrogram', fontsize=14, fontweight='bold')
    plt.xlabel('Sample Index or (Cluster Size)')
    plt.ylabel('Distance')
    
    # Add horizontal line for cut
    if n_clusters:
        # Find cut height
        cut_height = linkage_matrix[-n_clusters, 2]
        plt.axhline(y=cut_height, color='red', linestyle='--', alpha=0.5)
        plt.text(plt.xlim()[1]*0.5, cut_height*1.05, f'Cut for {n_clusters} clusters', 
                ha='center', bbox=dict(boxstyle='round', facecolor='wheat'))
    
    plt.tight_layout()
    plt.savefig('figure_dendrogram.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Get cluster labels
    if n_clusters:
        hier_labels = fcluster(linkage_matrix, n_clusters, criterion='maxclust')
        return hier_labels - 1  # Convert to 0-indexed
    
    return None

# ===== 4. MULTIPLE CLUSTERING METHODS =====

def perform_multiple_clustering(X_scaled, n_clusters):
    """
    Apply multiple clustering algorithms
    """
    print(f"\n=== COMPARING CLUSTERING METHODS (k={n_clusters}) ===")
    
    clustering_methods = {
        'K-means': KMeans(n_clusters=n_clusters, random_state=42, n_init=20),
        'Hierarchical': AgglomerativeClustering(n_clusters=n_clusters, linkage='ward'),
        'Gaussian Mixture': GaussianMixture(n_components=n_clusters, random_state=42, n_init=10)
    }
    
    results = {}
    
    for name, clusterer in clustering_methods.items():
        # Fit and predict
        if name == 'Gaussian Mixture':
            labels = clusterer.fit_predict(X_scaled)
        else:
            labels = clusterer.fit_predict(X_scaled)
        
        # Calculate metrics
        silhouette = silhouette_score(X_scaled, labels)
        calinski = calinski_harabasz_score(X_scaled, labels)
        davies_bouldin = davies_bouldin_score(X_scaled, labels)
        
        results[name] = {
            'labels': labels,
            'silhouette': silhouette,
            'calinski': calinski,
            'davies_bouldin': davies_bouldin,
            'n_per_cluster': np.bincount(labels)
        }
        
        print(f"\n{name}:")
        print(f"  Silhouette Score: {silhouette:.3f}")
        print(f"  Calinski-Harabasz: {calinski:.1f}")
        print(f"  Davies-Bouldin: {davies_bouldin:.3f}")
        print(f"  Cluster sizes: {np.bincount(labels)}")
    
    # Choose best method based on silhouette score
    best_method = max(results.keys(), key=lambda k: results[k]['silhouette'])
    print(f"\nBest method by silhouette score: {best_method}")
    
    return results, best_method

# ===== 5. CLUSTER VISUALIZATION =====

def visualize_clusters(X_scaled, labels, df_cluster, method_name):
    """
    Visualize clusters using PCA and t-SNE
    """
    print(f"\n=== VISUALIZING CLUSTERS ({method_name}) ===")
    
    fig, axes = plt.subplots(2, 2, figsize=(14, 12))
    
    # 1. PCA visualization
    ax = axes[0, 0]
    pca = PCA(n_components=2, random_state=42)
    X_pca = pca.fit_transform(X_scaled)
    
    for i in np.unique(labels):
        mask = labels == i
        ax.scatter(X_pca[mask, 0], X_pca[mask, 1], 
                  c=CLUSTER_COLORS[i % len(CLUSTER_COLORS)], 
                  label=f'Cluster {i+1} (n={mask.sum()})',
                  alpha=0.6, edgecolor='black', linewidth=0.5)
    
    ax.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%})')
    ax.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%})')
    ax.set_title('PCA Visualization', fontweight='bold')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # 2. t-SNE visualization
    ax = axes[0, 1]
    print("Computing t-SNE...")
    tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, len(X_scaled)-1))
    X_tsne = tsne.fit_transform(X_scaled)
    
    for i in np.unique(labels):
        mask = labels == i
        ax.scatter(X_tsne[mask, 0], X_tsne[mask, 1], 
                  c=CLUSTER_COLORS[i % len(CLUSTER_COLORS)], 
                  label=f'Cluster {i+1}',
                  alpha=0.6, edgecolor='black', linewidth=0.5)
    
    ax.set_xlabel('t-SNE 1')
    ax.set_ylabel('t-SNE 2')
    ax.set_title('t-SNE Visualization', fontweight='bold')
    
    # 3. Cluster characteristics - Score Change
    ax = axes[1, 0]
    cluster_score_change = []
    for i in np.unique(labels):
        mask = labels == i
        cluster_score_change.append(df_cluster.loc[df_cluster.index[mask], 'Score_Change'])
    
    bp = ax.boxplot(cluster_score_change, patch_artist=True)
    for i, patch in enumerate(bp['boxes']):
        patch.set_facecolor(CLUSTER_COLORS[i % len(CLUSTER_COLORS)])
        patch.set_alpha(0.7)
    
    ax.set_xlabel('Cluster')
    ax.set_ylabel('Score Change')
    ax.set_title('Score Change by Cluster', fontweight='bold')
    ax.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    
    # 4. Cluster characteristics - Math Anxiety
    ax = axes[1, 1]
    cluster_anxiety = []
    for i in np.unique(labels):
        mask = labels == i
        cluster_anxiety.append(df_cluster.loc[df_cluster.index[mask], 'MASR_Mean'])
    
    bp = ax.boxplot(cluster_anxiety, patch_artist=True)
    for i, patch in enumerate(bp['boxes']):
        patch.set_facecolor(CLUSTER_COLORS[i % len(CLUSTER_COLORS)])
        patch.set_alpha(0.7)
    
    ax.set_xlabel('Cluster')
    ax.set_ylabel('Math Anxiety (MASR)')
    ax.set_title('Math Anxiety by Cluster', fontweight='bold')
    
    plt.suptitle(f'Cluster Analysis Results ({method_name})', fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig(f'figure_clusters_{method_name.lower().replace(" ", "_")}.png', 
               dpi=300, bbox_inches='tight')
    plt.show()

# ===== 6. CLUSTER PROFILING =====

def profile_clusters(df_cluster, labels, features, scaler):
    """
    Create detailed profiles for each cluster
    """
    print("\n=== CLUSTER PROFILING ===")
    
    df_cluster['cluster'] = labels
    
    # Calculate cluster centers (in original scale)
    cluster_profiles = []
    
    for i in np.unique(labels):
        cluster_data = df_cluster[df_cluster['cluster'] == i]
        
        profile = {
            'cluster': i + 1,
            'n': len(cluster_data),
            'score_change_mean': cluster_data['Score_Change'].mean(),
            'score_change_sd': cluster_data['Score_Change'].std(),
            'improved_pct': (cluster_data['Score_Change'] > 0).mean() * 100,
            'masr_mean': cluster_data['MASR_Mean'].mean(),
            'wm_mean': cluster_data['Working_Memory_Score'].mean()
        }
        
        # Add top distinguishing features
        feature_means = cluster_data[features].mean()
        overall_means = df_cluster[features].mean()
        z_scores = (feature_means - overall_means) / df_cluster[features].std()
        
        # Top positive and negative features
        top_positive = z_scores.nlargest(5)
        top_negative = z_scores.nsmallest(5)
        
        profile['distinguishing_features'] = {
            'high': [(feat.replace('liwc1_', ''), z) for feat, z in top_positive.items()],
            'low': [(feat.replace('liwc1_', ''), z) for feat, z in top_negative.items()]
        }
        
        cluster_profiles.append(profile)
    
    # Print profiles
    for profile in cluster_profiles:
        print(f"\n{'='*50}")
        print(f"CLUSTER {profile['cluster']} (n={profile['n']})")
        print(f"{'='*50}")
        print(f"Score Change: {profile['score_change_mean']:.3f} ± {profile['score_change_sd']:.3f}")
        print(f"Improved: {profile['improved_pct']:.1f}%")
        print(f"Math Anxiety: {profile['masr_mean']:.2f}")
        print(f"Working Memory: {profile['wm_mean']:.1f}")
        
        print("\nDistinguishing features (HIGH):")
        for feat, z in profile['distinguishing_features']['high']:
            print(f"  {feat}: z = {z:.2f}")
        
        print("\nDistinguishing features (LOW):")
        for feat, z in profile['distinguishing_features']['low']:
            print(f"  {feat}: z = {z:.2f}")
    
    # Create radar chart for cluster profiles
    create_cluster_radar_chart(df_cluster, labels, features)
    
    return cluster_profiles

# ===== 7. RADAR CHART FOR CLUSTERS =====

def create_cluster_radar_chart(df_cluster, labels, features):
    """
    Create radar chart showing cluster profiles
    """
    # Select key features for radar chart
    radar_features = ['liwc1_anx', 'liwc1_negemo', 'liwc1_posemo', 'liwc1_cogproc', 
                     'liwc1_insight', 'liwc1_i', 'liwc1_focusfuture', 'liwc1_problem_focus']
    radar_features = [f for f in radar_features if f in features]
    
    if len(radar_features) < 4:
        print("Not enough features for radar chart")
        return
    
    # Calculate mean values for each cluster
    cluster_means = df_cluster.groupby('cluster')[radar_features].mean()
    
    # Standardize to 0-1 range for visualization
    cluster_means_scaled = (cluster_means - cluster_means.min()) / (cluster_means.max() - cluster_means.min())
    
    # Create radar chart
    fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))
    
    # Set up angles
    angles = np.linspace(0, 2*np.pi, len(radar_features), endpoint=False).tolist()
    angles += angles[:1]  # Complete the circle
    
    # Plot each cluster
    for idx, (cluster, row) in enumerate(cluster_means_scaled.iterrows()):
        values = row.tolist()
        values += values[:1]  # Complete the circle
        
        ax.plot(angles, values, 'o-', linewidth=2, 
               color=CLUSTER_COLORS[cluster % len(CLUSTER_COLORS)],
               label=f'Cluster {cluster+1}')
        ax.fill(angles, values, alpha=0.25, 
               color=CLUSTER_COLORS[cluster % len(CLUSTER_COLORS)])
    
    # Customize chart
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels([f.replace('liwc1_', '') for f in radar_features], size=10)
    ax.set_ylim(0, 1)
    ax.set_title('Cluster Profiles - Language Features', size=14, fontweight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.1))
    ax.grid(True)
    
    plt.tight_layout()
    plt.savefig('figure_cluster_radar.png', dpi=300, bbox_inches='tight')
    plt.show()

# ===== 8. STABILITY ANALYSIS =====

def analyze_cluster_stability(X_scaled, n_clusters, n_bootstrap=100):
    """
    Analyze cluster stability using bootstrap
    """
    print(f"\n=== CLUSTER STABILITY ANALYSIS (n_bootstrap={n_bootstrap}) ===")
    
    n_samples = X_scaled.shape[0]
    stability_matrix = np.zeros((n_samples, n_samples))
    
    for i in range(n_bootstrap):
        # Bootstrap sample
        indices = np.random.choice(n_samples, n_samples, replace=True)
        X_boot = X_scaled[indices]
        
        # Cluster
        kmeans = KMeans(n_clusters=n_clusters, random_state=i, n_init=10)
        labels_boot = kmeans.fit_predict(X_boot)
        
        # Update stability matrix
        for j in range(n_samples):
            for k in range(j+1, n_samples):
                if labels_boot[j] == labels_boot[k]:
                    stability_matrix[indices[j], indices[k]] += 1
                    stability_matrix[indices[k], indices[j]] += 1
    
    # Normalize
    stability_matrix /= n_bootstrap
    
    # Calculate average stability for each sample
    avg_stability = np.mean(stability_matrix, axis=1)
    
    print(f"Average stability: {np.mean(avg_stability):.3f}")
    print(f"Min stability: {np.min(avg_stability):.3f}")
    print(f"Max stability: {np.max(avg_stability):.3f}")
    
    # Identify unstable samples
    unstable_threshold = 0.5
    unstable_samples = np.where(avg_stability < unstable_threshold)[0]
    print(f"Unstable samples (stability < {unstable_threshold}): {len(unstable_samples)}")
    
    return stability_matrix, avg_stability

# ===== MAIN EXECUTION =====

def main():
    """Main execution function"""
    
    # Load data
    print("Loading data with engineered features...")
    df = pd.read_csv('k12_math_anxiety_engineered.csv')
    
    # Prepare clustering data
    df_cluster, X_scaled, features, scaler = prepare_clustering_data(df)
    
    # Determine optimal number of clusters
    cluster_results, optimal_k = determine_optimal_clusters(X_scaled)
    
    # Allow manual override if needed
    n_clusters = optimal_k
    print(f"\nUsing k = {n_clusters} clusters")
    
    # Hierarchical clustering analysis
    hier_labels = perform_hierarchical_clustering(X_scaled, n_clusters)
    
    # Compare multiple clustering methods
    method_results, best_method = perform_multiple_clustering(X_scaled, n_clusters)
    
    # Use best method for final analysis
    best_labels = method_results[best_method]['labels']
    
    # Visualize clusters
    visualize_clusters(X_scaled, best_labels, df_cluster, best_method)
    
    # Profile clusters
    cluster_profiles = profile_clusters(df_cluster, best_labels, features, scaler)
    
    # Analyze stability
    stability_matrix, avg_stability = analyze_cluster_stability(X_scaled, n_clusters)
    
    # Add stability to dataframe
    df_cluster['cluster'] = best_labels
    df_cluster['stability'] = avg_stability
    
    # Save results
    df_cluster.to_csv('k12_clustering_results.csv', index=False)
    
    # Save cluster profiles
    profiles_df = pd.DataFrame(cluster_profiles)
    profiles_df.to_csv('k12_cluster_profiles.csv', index=False)
    
    print("\n✓ Clustering results saved to 'k12_clustering_results.csv'")
    print("✓ Cluster profiles saved to 'k12_cluster_profiles.csv'")
    
    print("\n=== CLUSTERING ANALYSIS COMPLETE ===")
    
    return df_cluster, best_labels, cluster_profiles

if __name__ == "__main__":
    df_cluster, labels, profiles = main()

#!/usr/bin/env python3
"""
K-12 Math Anxiety Study - Optimized Clustering Analysis (k=3)
Improved approach with feature selection, outlier handling, and k=3
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans, DBSCAN
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

# APA-style plotting
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams.update({
    'figure.figsize': (12, 8),
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 11,
    'font.family': 'sans-serif',
    'axes.spines.top': False,
    'axes.spines.right': False
})

# Color palette for 3 clusters
CLUSTER_COLORS = {
    0: '#E74C3C',  # Red
    1: '#3498DB',  # Blue
    2: '#2ECC71'   # Green
}

CLUSTER_NAMES = {
    0: 'Type A',
    1: 'Type B', 
    2: 'Type C'
}

# ===== 1. FEATURE SELECTION AND DATA PREPARATION =====

def prepare_optimized_data(df):
    """
    Prepare data with feature selection and outlier detection
    """
    print("=== OPTIMIZED DATA PREPARATION ===")
    
    # Focus on treatment group
    df_treat = df[df['Group'] == 1].copy()
    
    # Core feature categories (reduced set)
    core_features = {
        'emotional': ['liwc1_anx', 'liwc1_negemo', 'liwc1_posemo'],
        'cognitive': ['liwc1_cogproc', 'liwc1_insight', 'liwc1_cause'],
        'self_focus': ['liwc1_i', 'liwc1_we'],
        'temporal': ['liwc1_focuspast', 'liwc1_focuspresent', 'liwc1_focusfuture'],
        'engineered': ['liwc1_emo_regulation', 'liwc1_cognitive_complexity', 
                      'liwc1_problem_focus', 'liwc1_anxiety_intensity']
    }
    
    # Flatten feature list
    selected_features = []
    for features in core_features.values():
        selected_features.extend(features)
    
    # Keep only available features
    selected_features = [f for f in selected_features if f in df_treat.columns]
    
    # Create analysis dataset
    required_cols = selected_features + ['ID', 'MASR_Mean', 'Score_Change', 
                                         'Working_Memory_Score', 'Class_Type']
    df_analysis = df_treat[required_cols].dropna()
    
    print(f"Initial samples: {len(df_analysis)}")
    print(f"Selected features: {len(selected_features)}")
    
    # Extract feature matrix
    X = df_analysis[selected_features].values
    
    # 1. Detect and remove outliers using DBSCAN
    print("\n1. Outlier Detection:")
    scaler_outlier = StandardScaler()
    X_scaled_outlier = scaler_outlier.fit_transform(X)
    
    # DBSCAN for outlier detection
    dbscan = DBSCAN(eps=3.0, min_samples=5)
    outlier_labels = dbscan.fit_predict(X_scaled_outlier)
    
    # Identify outliers (label = -1)
    outliers = outlier_labels == -1
    n_outliers = outliers.sum()
    print(f"  Outliers detected: {n_outliers}")
    
    # Remove outliers
    if n_outliers > 0 and n_outliers < len(df_analysis) * 0.1:  # Remove if less than 10%
        df_clean = df_analysis[~outliers].copy()
        X_clean = X[~outliers]
        print(f"  Removed {n_outliers} outliers")
    else:
        df_clean = df_analysis.copy()
        X_clean = X
        print("  No outliers removed")
    
    print(f"  Clean samples: {len(df_clean)}")
    
    # 2. Feature importance analysis
    print("\n2. Feature Importance Analysis:")
    
    # Use Random Forest to identify important features
    rf = RandomForestRegressor(n_estimators=100, random_state=42)
    rf.fit(X_clean, df_clean['Score_Change'])
    
    feature_importance = pd.DataFrame({
        'feature': selected_features,
        'importance': rf.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("\nTop 10 important features:")
    print(feature_importance.head(10))
    
    # Select top features
    n_features = min(15, len(selected_features))  # Use at most 15 features
    top_features = feature_importance.head(n_features)['feature'].tolist()
    
    # 3. Create final feature matrix
    X_final = df_clean[top_features].values
    
    # Use RobustScaler (less sensitive to outliers)
    scaler = RobustScaler()
    X_scaled = scaler.fit_transform(X_final)
    
    print(f"\n3. Final Data Shape:")
    print(f"  Samples: {X_scaled.shape[0]}")
    print(f"  Features: {X_scaled.shape[1]}")
    
    return df_clean, X_scaled, top_features, scaler

# ===== 2. OPTIMAL K=3 CLUSTERING =====

def perform_k3_clustering(X_scaled, df_clean):
    """
    Perform k=3 clustering with multiple initializations
    """
    print("\n=== K=3 CLUSTERING ANALYSIS ===")
    
    # Try multiple random initializations to find best result
    best_score = -1
    best_kmeans = None
    best_labels = None
    
    print("\nTrying multiple initializations...")
    for i in range(20):
        kmeans = KMeans(n_clusters=3, random_state=i, n_init=20, max_iter=500)
        labels = kmeans.fit_predict(X_scaled)
        
        # Check if clusters are reasonably balanced
        counts = np.bincount(labels)
        min_size = counts.min()
        
        # Calculate silhouette score
        score = silhouette_score(X_scaled, labels)
        
        # Prefer balanced clusters
        if min_size >= 3 and score > best_score:
            best_score = score
            best_kmeans = kmeans
            best_labels = labels
    
    print(f"\nBest clustering found:")
    print(f"  Silhouette score: {best_score:.3f}")
    print(f"  Cluster sizes: {np.bincount(best_labels)}")
    
    # Calculate additional metrics
    calinski = calinski_harabasz_score(X_scaled, best_labels)
    print(f"  Calinski-Harabasz: {calinski:.1f}")
    
    return best_kmeans, best_labels

# ===== 3. CLUSTER INTERPRETATION =====

def interpret_clusters(df_clean, labels, features):
    """
    Create meaningful interpretations of the 3 clusters
    """
    print("\n=== CLUSTER INTERPRETATION ===")
    
    df_clean['cluster'] = labels
    
    # Overall statistics for each cluster
    for i in range(3):
        cluster_data = df_clean[df_clean['cluster'] == i]
        n = len(cluster_data)
        
        print(f"\n{'='*60}")
        print(f"CLUSTER {i+1}: {CLUSTER_NAMES[i]} (n={n}, {n/len(df_clean)*100:.1f}%)")
        print(f"{'='*60}")
        
        # Basic statistics
        print(f"\nPerformance:")
        print(f"  Score Change: {cluster_data['Score_Change'].mean():.3f} ± {cluster_data['Score_Change'].std():.3f}")
        print(f"  Improved: {(cluster_data['Score_Change'] > 0).mean()*100:.1f}%")
        print(f"  Significantly improved (>0.5): {(cluster_data['Score_Change'] > 0.5).mean()*100:.1f}%")
        
        print(f"\nBaseline Characteristics:")
        print(f"  Math Anxiety: {cluster_data['MASR_Mean'].mean():.2f} ± {cluster_data['MASR_Mean'].std():.2f}")
        print(f"  Working Memory: {cluster_data['Working_Memory_Score'].mean():.1f} ± {cluster_data['Working_Memory_Score'].std():.1f}")
        
        # Class type distribution
        if 'Class_Type' in cluster_data.columns:
            fast_track = cluster_data['Class_Type'].isin(['Pearl', 'Amber']).mean() * 100
            print(f"  Fast-track students: {fast_track:.1f}%")
        
        # Key distinguishing features
        print(f"\nDistinguishing Language Features:")
        
        # Calculate z-scores for features
        for feat in features[:10]:  # Top 10 features
            if feat in cluster_data.columns:
                cluster_mean = cluster_data[feat].mean()
                overall_mean = df_clean[feat].mean()
                overall_std = df_clean[feat].std()
                z_score = (cluster_mean - overall_mean) / overall_std if overall_std > 0 else 0
                
                if abs(z_score) > 0.5:  # Only show meaningful differences
                    direction = "↑" if z_score > 0 else "↓"
                    print(f"    {feat.replace('liwc1_', '')}: {direction} {abs(z_score):.2f} SD")

# ===== 4. CLUSTER VISUALIZATION =====

def visualize_k3_clusters(X_scaled, labels, df_clean, features):
    """
    Create comprehensive visualizations for k=3 clusters
    """
    print("\n=== CREATING VISUALIZATIONS ===")
    
    fig = plt.figure(figsize=(16, 12))
    
    # 1. PCA visualization
    ax1 = plt.subplot(2, 3, 1)
    pca = PCA(n_components=2)
    X_pca = pca.fit_transform(X_scaled)
    
    for i in range(3):
        mask = labels == i
        ax1.scatter(X_pca[mask, 0], X_pca[mask, 1], 
                   c=CLUSTER_COLORS[i], 
                   label=f'{CLUSTER_NAMES[i]} (n={mask.sum()})',
                   alpha=0.6, edgecolor='black', linewidth=0.5, s=80)
    
    ax1.set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%})')
    ax1.set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%})')
    ax1.set_title('Cluster Distribution (PCA)', fontweight='bold')
    ax1.legend()
    
    # 2. Score Change by Cluster
    ax2 = plt.subplot(2, 3, 2)
    cluster_scores = [df_clean[df_clean['cluster'] == i]['Score_Change'] for i in range(3)]
    
    bp = ax2.boxplot(cluster_scores, patch_artist=True, labels=[CLUSTER_NAMES[i] for i in range(3)])
    for i, patch in enumerate(bp['boxes']):
        patch.set_facecolor(CLUSTER_COLORS[i])
        patch.set_alpha(0.7)
    
    ax2.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    ax2.set_ylabel('Score Change')
    ax2.set_title('Performance by Cluster', fontweight='bold')
    
    # Add mean values
    for i, scores in enumerate(cluster_scores):
        ax2.text(i+1, scores.mean(), f'μ={scores.mean():.2f}', 
                ha='center', va='bottom', fontweight='bold')
    
    # 3. Math Anxiety by Cluster
    ax3 = plt.subplot(2, 3, 3)
    cluster_anxiety = [df_clean[df_clean['cluster'] == i]['MASR_Mean'] for i in range(3)]
    
    bp = ax3.boxplot(cluster_anxiety, patch_artist=True, labels=[CLUSTER_NAMES[i] for i in range(3)])
    for i, patch in enumerate(bp['boxes']):
        patch.set_facecolor(CLUSTER_COLORS[i])
        patch.set_alpha(0.7)
    
    ax3.set_ylabel('Math Anxiety (MASR)')
    ax3.set_title('Baseline Anxiety by Cluster', fontweight='bold')
    
    # 4. Feature Heatmap
    ax4 = plt.subplot(2, 3, (4, 6))
    
    # Calculate mean values for each cluster
    cluster_means = df_clean.groupby('cluster')[features[:12]].mean()
    
    # Standardize for visualization
    cluster_means_z = (cluster_means - cluster_means.mean()) / cluster_means.std()
    cluster_means_z.index = [CLUSTER_NAMES[i] for i in range(3)]
    cluster_means_z.columns = [col.replace('liwc1_', '') for col in cluster_means_z.columns]
    
    sns.heatmap(cluster_means_z.T, cmap='RdBu_r', center=0, 
               annot=True, fmt='.2f', cbar_kws={'label': 'Z-score'},
               xticklabels=True, yticklabels=True, ax=ax4)
    ax4.set_title('Language Feature Profiles (Z-scores)', fontweight='bold')
    
    plt.suptitle('K=3 Clustering Analysis Results', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('figure_k3_clustering_results.png', dpi=300, bbox_inches='tight')
    plt.show()

# ===== 5. CLUSTER STABILITY ANALYSIS =====

def analyze_k3_stability(X_scaled, n_bootstrap=50):
    """
    Simplified stability analysis for k=3
    """
    print("\n=== CLUSTER STABILITY ANALYSIS ===")
    
    n_samples = X_scaled.shape[0]
    co_occurrence = np.zeros((n_samples, n_samples))
    
    print(f"Running {n_bootstrap} bootstrap iterations...")
    
    for i in range(n_bootstrap):
        # Bootstrap sample
        indices = np.random.choice(n_samples, n_samples, replace=True)
        X_boot = X_scaled[indices]
        
        # Cluster
        kmeans = KMeans(n_clusters=3, random_state=i, n_init=10)
        labels = kmeans.fit_predict(X_boot)
        
        # Update co-occurrence matrix
        for j in range(n_samples):
            for k in range(j+1, n_samples):
                if labels[j] == labels[k]:
                    co_occurrence[indices[j], indices[k]] += 1
                    co_occurrence[indices[k], indices[j]] += 1
    
    # Normalize
    co_occurrence /= n_bootstrap
    
    # Calculate stability score for each sample
    stability_scores = []
    for i in range(n_samples):
        # Average co-occurrence with samples in same final cluster
        same_cluster = np.where(labels == labels[i])[0]
        if len(same_cluster) > 1:
            stability = np.mean([co_occurrence[i, j] for j in same_cluster if i != j])
        else:
            stability = 0
        stability_scores.append(stability)
    
    stability_scores = np.array(stability_scores)
    
    print(f"\nStability Results:")
    print(f"  Mean stability: {np.mean(stability_scores):.3f}")
    print(f"  Stable samples (>0.7): {(stability_scores > 0.7).sum()} ({(stability_scores > 0.7).mean()*100:.1f}%)")
    print(f"  Unstable samples (<0.5): {(stability_scores < 0.5).sum()} ({(stability_scores < 0.5).mean()*100:.1f}%)")
    
    return stability_scores

# ===== 6. CLUSTER PROFILES SUMMARY =====

def create_cluster_summary(df_clean, features):
    """
    Create a summary table of cluster characteristics
    """
    print("\n=== CLUSTER SUMMARY TABLE ===")
    
    summary_data = []
    
    for i in range(3):
        cluster_data = df_clean[df_clean['cluster'] == i]
        
        # Calculate key metrics
        summary = {
            'Cluster': CLUSTER_NAMES[i],
            'N': len(cluster_data),
            'Percent': f"{len(cluster_data)/len(df_clean)*100:.1f}%",
            'Score_Change': f"{cluster_data['Score_Change'].mean():.3f} ± {cluster_data['Score_Change'].std():.3f}",
            'Improved_%': f"{(cluster_data['Score_Change'] > 0).mean()*100:.1f}%",
            'MASR': f"{cluster_data['MASR_Mean'].mean():.2f} ± {cluster_data['MASR_Mean'].std():.2f}",
            'WM': f"{cluster_data['Working_Memory_Score'].mean():.1f} ± {cluster_data['Working_Memory_Score'].std():.1f}"
        }
        
        # Add top 3 distinguishing features
        feature_zscores = []
        for feat in features[:10]:
            if feat in cluster_data.columns:
                cluster_mean = cluster_data[feat].mean()
                overall_mean = df_clean[feat].mean()
                overall_std = df_clean[feat].std()
                z_score = (cluster_mean - overall_mean) / overall_std if overall_std > 0 else 0
                feature_zscores.append((feat.replace('liwc1_', ''), z_score))
        
        # Sort by absolute z-score
        feature_zscores.sort(key=lambda x: abs(x[1]), reverse=True)
        
        # Top 3 features
        top_features = []
        for feat, z in feature_zscores[:3]:
            direction = "↑" if z > 0 else "↓"
            top_features.append(f"{feat}{direction}")
        
        summary['Top_Features'] = ', '.join(top_features)
        
        summary_data.append(summary)
    
    # Create DataFrame
    summary_df = pd.DataFrame(summary_data)
    print("\n", summary_df.to_string(index=False))
    
    # Save summary
    summary_df.to_csv('k3_cluster_summary.csv', index=False)
    print("\n✓ Cluster summary saved to 'k3_cluster_summary.csv'")
    
    return summary_df

# ===== 7. PRACTICAL RECOMMENDATIONS =====

def generate_recommendations(df_clean):
    """
    Generate practical recommendations based on clusters
    """
    print("\n=== PRACTICAL RECOMMENDATIONS ===")
    
    for i in range(3):
        cluster_data = df_clean[df_clean['cluster'] == i]
        
        print(f"\n{CLUSTER_NAMES[i]}:")
        
        # Analyze characteristics
        avg_change = cluster_data['Score_Change'].mean()
        avg_anxiety = cluster_data['MASR_Mean'].mean()
        
        # Generate tailored recommendations
        if avg_change > 0.1:
            print("  ✓ Current approach working well for this group")
            print("  → Continue with similar interventions")
        elif avg_change < -0.1:
            print("  ✗ Current approach not effective")
            print("  → Need alternative intervention strategies")
        else:
            print("  ~ Mixed results")
            print("  → Consider individual differences within group")
        
        # Specific recommendations based on patterns
        if 'liwc1_cogproc' in cluster_data.columns:
            cog_level = cluster_data['liwc1_cogproc'].mean()
            overall_cog = df_clean['liwc1_cogproc'].mean()
            
            if cog_level > overall_cog * 1.2:
                print("  → High cognitive processing: May benefit from structured problem-solving")
            elif cog_level < overall_cog * 0.8:
                print("  → Low cognitive processing: Need more cognitive engagement strategies")
        
        if avg_anxiety > df_clean['MASR_Mean'].mean():
            print("  → Higher anxiety: Focus on anxiety reduction techniques")

# ===== MAIN EXECUTION =====

def main():
    """Main execution function"""
    
    # Load data
    print("Loading data with engineered features...")
    df = pd.read_csv('k12_math_anxiety_engineered.csv')
    
    # Prepare optimized data
    df_clean, X_scaled, features, scaler = prepare_optimized_data(df)
    
    # Perform k=3 clustering
    kmeans, labels = perform_k3_clustering(X_scaled, df_clean)
    
    # Interpret clusters
    interpret_clusters(df_clean, labels, features)
    
    # Visualize results
    visualize_k3_clusters(X_scaled, labels, df_clean, features)
    
    # Analyze stability
    stability_scores = analyze_k3_stability(X_scaled)
    
    # Add results to dataframe
    df_clean['cluster'] = labels
    df_clean['cluster_name'] = df_clean['cluster'].map(CLUSTER_NAMES)
    df_clean['stability'] = stability_scores
    
    # Create summary
    summary_df = create_cluster_summary(df_clean, features)
    
    # Generate recommendations
    generate_recommendations(df_clean)
    
    # Save final results
    df_clean.to_csv('k3_clustering_final_results.csv', index=False)
    print("\n✓ Final results saved to 'k3_clustering_final_results.csv'")
    
    # Save cluster centers for future use
    cluster_centers = pd.DataFrame(
        scaler.inverse_transform(kmeans.cluster_centers_),
        columns=features,
        index=[CLUSTER_NAMES[i] for i in range(3)]
    )
    cluster_centers.to_csv('k3_cluster_centers.csv')
    print("✓ Cluster centers saved to 'k3_cluster_centers.csv'")
    
    print("\n=== K=3 CLUSTERING ANALYSIS COMPLETE ===")
    
    return df_clean, labels, summary_df

if __name__ == "__main__":
    df_final, labels, summary = main()

#!/usr/bin/env python3
"""
K-12 Math Anxiety Intervention Study - LIWC Analysis Part 1
Feature Analysis by Math Anxiety and Working Memory
Following MHC Dataset Poster methodology
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from wordcloud import WordCloud
from collections import Counter
import re
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# APA-style plotting parameters
plt.style.use('seaborn-v0_8-whitegrid')

# APA-compatible color palette
APA_COLORS = {
    'q1': '#0072B2',      # Blue (lowest anxiety)
    'q2': '#56B4E9',      # Sky blue
    'q3': '#E69F00',      # Orange
    'q4': '#D55E00',      # Vermillion (highest anxiety)
    'low': '#0072B2',     # Blue
    'high': '#D55E00',    # Vermillion
    'accent': '#999999'   # Gray
}

plt.rcParams.update({
    'figure.figsize': (10, 8),
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 11,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 9,
    'font.family': 'sans-serif',
    'axes.spines.top': False,
    'axes.spines.right': False
})

# ===== 1. DATA PREPARATION =====

def prepare_data_for_analysis(df):
    """
    Prepare data for analysis, combining first and second writing samples
    for treatment group to increase sample size
    """
    
    # Filter treatment group only
    df_treat = df[df['Group'] == 1].copy()
    
    print("=== DATA PREPARATION ===")
    print(f"Treatment group participants: {len(df_treat)}")
    
    # Create a list to store all writing samples
    all_samples = []
    
    # Process first writing samples
    first_samples = df_treat[~df_treat['@1_English_Writing'].isna()].copy()
    first_samples['writing_time'] = 1
    first_samples['sample_id'] = first_samples['ID'].astype(str) + '_T1'
    
    # Process second writing samples  
    second_samples = df_treat[~df_treat['@2_English_Writing'].isna()].copy()
    second_samples['writing_time'] = 2
    second_samples['sample_id'] = second_samples['ID'].astype(str) + '_T2'
    
    print(f"First writing samples: {len(first_samples)}")
    print(f"Second writing samples: {len(second_samples)}")
    
    # Rename LIWC columns for consistency
    liwc1_mapping = {col: col.replace('liwc1_', '') for col in first_samples.columns if col.startswith('liwc1_')}
    liwc2_mapping = {col: col.replace('liwc2_', '') for col in second_samples.columns if col.startswith('liwc2_')}
    
    first_samples.rename(columns=liwc1_mapping, inplace=True)
    second_samples.rename(columns=liwc2_mapping, inplace=True)
    
    # Combine samples
    common_cols = list(set(first_samples.columns) & set(second_samples.columns))
    df_combined = pd.concat([first_samples[common_cols], second_samples[common_cols]], ignore_index=True)
    
    print(f"Total writing samples for analysis: {len(df_combined)}")
    
    # Add anxiety quartiles for reference
    df_combined['masr_quartile'] = pd.qcut(df_combined['MASR_Mean'], 
                                           q=4, 
                                           labels=['Q1', 'Q2', 'Q3', 'Q4'])
    
    # Create anxiety groups based on meaningful cutoffs
    # Given low overall anxiety (median ~2.21), use 2.5 and 3.0 as cutoffs
    df_combined['anxiety_group'] = pd.cut(df_combined['MASR_Mean'],
                                          bins=[0, 2.5, 3.0, 5],
                                          labels=['Low (<2.5)', 'Moderate (2.5-3.0)', 'High (>3.0)'])
    
    # Binary classification for main analyses
    df_combined['anxiety_level'] = df_combined['MASR_Mean'].apply(
        lambda x: 'High' if x >= 2.5 else 'Low'
    )
    
    # Print distribution
    print("\nAnxiety distribution (MASR scores):")
    print(f"Mean: {df_combined['MASR_Mean'].mean():.2f}, Median: {df_combined['MASR_Mean'].median():.2f}")
    print(f"Range: {df_combined['MASR_Mean'].min():.2f} - {df_combined['MASR_Mean'].max():.2f}")
    print("\nAnxiety group distribution:")
    print(df_combined['anxiety_group'].value_counts().sort_index())
    print(f"\nBinary classification (>=2.5 as High):")
    print(df_combined['anxiety_level'].value_counts())
    
    # Add working memory groups
    median_wm = df_combined['Working_Memory_Score'].median()
    df_combined['wm_group'] = df_combined['Working_Memory_Score'].apply(
        lambda x: 'High WM' if x > median_wm else 'Low WM'
    )
    
    return df_combined, df_treat

# ===== 2. WORD CLOUD ANALYSIS =====

def create_anxiety_wordclouds(df_combined):
    """
    Create word clouds comparing different anxiety level students' writing
    Using meaningful cutoffs for K-12 population
    """
    
    print("\n=== WORD CLOUD ANALYSIS ===")
    
    # Three-panel comparison: Low (<2.5), Moderate (2.5-3.0), High (>3.0)
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # Define groups and colors
    groups = ['Low (<2.5)', 'Moderate (2.5-3.0)', 'High (>3.0)']
    colormaps = ['Blues', 'Oranges', 'Reds']
    
    for idx, (group, cmap) in enumerate(zip(groups, colormaps)):
        # Get text for this group
        group_text = ' '.join(
            df_combined[df_combined['anxiety_group'] == group]['@1_English_Writing']
            .dropna().astype(str)
        )
        
        if group_text:
            wc = WordCloud(width=600, height=400, 
                          background_color='white',
                          colormap=cmap,
                          max_words=100).generate(group_text)
            axes[idx].imshow(wc, interpolation='bilinear')
            axes[idx].set_title(f'{group}\n(n={len(df_combined[df_combined["anxiety_group"] == group])})', 
                               fontsize=12, fontweight='bold', pad=10)
            axes[idx].axis('off')
    
    plt.suptitle('Word Clouds by Math Anxiety Level (K-12 Students)', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('figure1_wordclouds_anxiety_groups.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure1_wordclouds_anxiety_groups.pdf', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Binary comparison for main analyses
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 7))
    
    low_text = ' '.join(df_combined[df_combined['MASR_Mean'] < 2.5]['@1_English_Writing'].dropna().astype(str))
    high_text = ' '.join(df_combined[df_combined['MASR_Mean'] >= 2.5]['@1_English_Writing'].dropna().astype(str))
    
    if low_text:
        wc_low = WordCloud(width=700, height=400, 
                          background_color='white',
                          colormap='Blues',
                          max_words=100).generate(low_text)
        ax1.imshow(wc_low, interpolation='bilinear')
        ax1.set_title(f'Lower Anxiety (MASR < 2.5)\nn={len(df_combined[df_combined["MASR_Mean"] < 2.5])}', 
                     fontsize=14, fontweight='bold', pad=15)
        ax1.axis('off')
    
    if high_text:
        wc_high = WordCloud(width=700, height=400, 
                           background_color='white',
                           colormap='Reds',
                           max_words=100).generate(high_text)
        ax2.imshow(wc_high, interpolation='bilinear')
        ax2.set_title(f'Higher Anxiety (MASR ≥ 2.5)\nn={len(df_combined[df_combined["MASR_Mean"] >= 2.5])}', 
                     fontsize=14, fontweight='bold', pad=15)
        ax2.axis('off')
    
    plt.tight_layout()
    plt.savefig('figure1b_wordclouds_binary.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Word frequency analysis for highest anxiety group
    print("\nWord frequency analysis:")
    
    def get_word_freq(text, top_n=20):
        """Get word frequency excluding common stop words"""
        words = re.findall(r'\b[a-z]+\b', text.lower())
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
                     'of', 'with', 'by', 'from', 'is', 'am', 'are', 'was', 'were', 'been',
                     'be', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would',
                     'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that',
                     'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'my'}
        words = [w for w in words if w not in stop_words and len(w) > 2]
        return Counter(words).most_common(top_n)
    
    # Compare highest anxiety group (>3.0) with others
    very_high_text = ' '.join(df_combined[df_combined['MASR_Mean'] > 3.0]['@1_English_Writing'].dropna().astype(str))
    others_text = ' '.join(df_combined[df_combined['MASR_Mean'] <= 3.0]['@1_English_Writing'].dropna().astype(str))
    
    if very_high_text:
        print(f"\nHighest Anxiety Group (MASR > 3.0, n={len(df_combined[df_combined['MASR_Mean'] > 3.0])}):")
        for word, count in get_word_freq(very_high_text, 15):
            print(f"  {word}: {count}")
    
    if others_text:
        print(f"\nOther Students (MASR ≤ 3.0, n={len(df_combined[df_combined['MASR_Mean'] <= 3.0])}):")
        for word, count in get_word_freq(others_text, 15):
            print(f"  {word}: {count}")

# ===== 3. ENHANCED QUARTILE HEATMAP (FROM MHC POSTER) =====

def plot_enhanced_heatmap_by_anxiety_groups(df, features):
    """
    Create enhanced Z-score heatmap for K-12 anxiety groups
    Using meaningful cutoffs: <2.5, 2.5-3.0, >3.0
    """
    
    print("\n=== ENHANCED ANXIETY GROUP HEATMAP ANALYSIS ===")
    
    # Create custom groups
    groups = ['Low (<2.5)', 'Moderate (2.5-3.0)', 'High (>3.0)']
    
    # Calculate group means and Z-scores
    group_means = df.groupby('anxiety_group')[features].mean()
    
    # Ensure all groups are present
    for group in groups:
        if group not in group_means.index:
            print(f"Warning: No students in {group} group")
    
    # Calculate Z-scores relative to overall mean and std
    overall_means = df[features].mean()
    overall_stds = df[features].std()
    
    z_scores = (group_means - overall_means) / overall_stds
    
    # Order features by High-Low difference
    if 'High (>3.0)' in z_scores.index and 'Low (<2.5)' in z_scores.index:
        high_low_diff = z_scores.loc['High (>3.0)'] - z_scores.loc['Low (<2.5)']
        ordered_features = high_low_diff.abs().sort_values(ascending=False).index.tolist()
        z_scores = z_scores[ordered_features]
    else:
        ordered_features = features
    
    # Create the heatmap
    plt.figure(figsize=(14, 10))
    
    # Create custom colormap
    cmap = sns.diverging_palette(250, 10, as_cmap=True)
    
    # Create mask for missing groups
    mask = z_scores.isna()
    
    # Plot heatmap
    ax = sns.heatmap(z_scores.T, 
                     cmap=cmap, 
                     center=0,
                     vmin=-2, vmax=2,
                     annot=True, 
                     fmt='.2f',
                     cbar_kws={'label': 'Z-Score'},
                     linewidths=0.5,
                     linecolor='gray',
                     mask=mask.T)
    
    # Add sample sizes to x-axis labels
    new_labels = []
    for group in z_scores.index:
        n = len(df[df['anxiety_group'] == group])
        new_labels.append(f'{group}\n(n={n})')
    ax.set_xticklabels(new_labels)
    
    plt.title('Language Features by Math Anxiety Level (K-12 Students)\n(Z-scores relative to overall mean)', 
             fontsize=14, fontweight='bold', pad=20)
    plt.xlabel('Math Anxiety Group', fontsize=12)
    plt.ylabel('LIWC Features', fontsize=12)
    
    # Highlight features with large differences
    ax = plt.gca()
    if 'High (>3.0)' in z_scores.index and 'Low (<2.5)' in z_scores.index:
        for i, feature in enumerate(ordered_features):
            diff = z_scores.loc['High (>3.0)', feature] - z_scores.loc['Low (<2.5)', feature]
            if abs(diff) > 0.5:  # Meaningful effect size
                ax.add_patch(plt.Rectangle((0, i-0.5), len(groups), 1, 
                                         fill=False, edgecolor='black', 
                                         linewidth=2))
    
    plt.tight_layout()
    plt.savefig('figure2_anxiety_group_heatmap.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure2_anxiety_group_heatmap.pdf', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print summary statistics
    print("\nFeatures with largest High-Low differences:")
    if 'High (>3.0)' in z_scores.index and 'Low (<2.5)' in z_scores.index:
        high_low_diff = z_scores.loc['High (>3.0)'] - z_scores.loc['Low (<2.5)']
        for feature in high_low_diff.abs().sort_values(ascending=False).head(10).index:
            diff = high_low_diff[feature]
            print(f"  {feature}: {diff:.3f} (Low={z_scores.loc['Low (<2.5)', feature]:.2f}, "
                  f"High={z_scores.loc['High (>3.0)', feature]:.2f})")
    
    # Also create traditional quartile heatmap for reference
    plot_enhanced_heatmap_by_quartile(df, features)
    """
    Create enhanced Z-score heatmap showing language features across MASR quartiles,
    ordered by Q4-Q1 difference for better interpretability.
    Directly adapted from MHC Dataset Poster Part 6
    """
    
    print("\n=== ENHANCED MASR QUARTILE HEATMAP ANALYSIS ===")
    
    # Calculate group means and Z-scores
    group_means = df.groupby(group_col)[features].mean()
    
    # Calculate Z-scores relative to overall mean and std
    overall_means = df[features].mean()
    overall_stds = df[features].std()
    
    z_scores = (group_means - overall_means) / overall_stds
    
    # Calculate Q4-Q1 difference for ordering
    if 'Q1' in z_scores.index and 'Q4' in z_scores.index:
        q4_q1_diff = z_scores.loc['Q4'] - z_scores.loc['Q1']
        # Order features by absolute difference
        ordered_features = q4_q1_diff.abs().sort_values(ascending=False).index.tolist()
        z_scores = z_scores[ordered_features]
    
    # Create the heatmap
    plt.figure(figsize=(12, 8))
    
    # Create custom colormap
    cmap = sns.diverging_palette(250, 10, as_cmap=True)
    
    # Plot heatmap
    sns.heatmap(z_scores.T, 
                cmap=cmap, 
                center=0,
                vmin=-2, vmax=2,
                annot=True, 
                fmt='.2f',
                cbar_kws={'label': 'Z-Score'},
                linewidths=0.5,
                linecolor='gray')
    
    plt.title('Language Features by Math Anxiety Quartile\n(Z-scores relative to overall mean)', 
             fontsize=14, fontweight='bold', pad=20)
    plt.xlabel('Math Anxiety Quartile', fontsize=12)
    plt.ylabel('LIWC Features', fontsize=12)
    
    # Highlight significant differences
    ax = plt.gca()
    for i, feature in enumerate(ordered_features):
        if 'Q1' in z_scores.index and 'Q4' in z_scores.index:
            diff = z_scores.loc['Q4', feature] - z_scores.loc['Q1', feature]
            if abs(diff) > 0.5:  # Meaningful effect size
                ax.add_patch(plt.Rectangle((0, i-0.5), 4, 1, 
                                         fill=False, edgecolor='black', 
                                         linewidth=2))
    
    plt.tight_layout()
    plt.savefig('figure2_quartile_heatmap.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure2_quartile_heatmap.pdf', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print summary statistics
    print("\nFeatures with largest Q4-Q1 differences:")
    if 'Q1' in z_scores.index and 'Q4' in z_scores.index:
        q4_q1_diff = z_scores.loc['Q4'] - z_scores.loc['Q1']
        for feature in q4_q1_diff.abs().sort_values(ascending=False).head(10).index:
            diff = q4_q1_diff[feature]
            print(f"  {feature}: {diff:.3f} (Q1={z_scores.loc['Q1', feature]:.2f}, "
                  f"Q4={z_scores.loc['Q4', feature]:.2f})")

# ===== 4. STATISTICAL COMPARISONS =====

def comprehensive_feature_analysis(df_combined):
    """
    Comprehensive statistical analysis of LIWC features by anxiety level
    Using meaningful cutoffs for K-12 population
    """
    
    print("\n=== COMPREHENSIVE FEATURE ANALYSIS ===")
    print("Comparing students with MASR ≥ 2.5 vs < 2.5")
    
    # Define all LIWC features to analyze
    liwc_features = ['WC', 'WPS', 'Analytic', 'Clout', 'Authentic', 'Tone',
                    'i', 'we', 'you', 'shehe', 'they', 
                    'posemo', 'negemo', 'anx', 'anger', 'sad',
                    'cogproc', 'insight', 'cause', 'discrep', 'tentat', 'certain',
                    'achieve', 'power', 'reward', 'risk',
                    'focuspast', 'focuspresent', 'focusfuture',
                    'work', 'leisure', 'home', 'money', 'relig', 'death']
    
    # Filter to available features
    available_features = [f for f in liwc_features if f in df_combined.columns]
    print(f"Analyzing {len(available_features)} LIWC features")
    
    # Perform comparisons for different cutoffs
    results_list = []
    
    # Analysis 1: MASR >= 2.5 vs < 2.5
    for feature in available_features:
        high = df_combined[df_combined['MASR_Mean'] >= 2.5][feature].dropna()
        low = df_combined[df_combined['MASR_Mean'] < 2.5][feature].dropna()
        
        if len(high) > 5 and len(low) > 5:
            # Test normality
            _, p_norm_high = stats.shapiro(high) if len(high) > 3 else (np.nan, 0)
            _, p_norm_low = stats.shapiro(low) if len(low) > 3 else (np.nan, 0)
            
            # Choose test based on normality
            if p_norm_high < 0.05 or p_norm_low < 0.05:
                stat, p_value = stats.mannwhitneyu(high, low)
                test_used = 'Mann-Whitney U'
                # Calculate effect size (rank-biserial correlation)
                n1, n2 = len(high), len(low)
                effect_size = 1 - (2*stat) / (n1*n2)
            else:
                stat, p_value = stats.ttest_ind(high, low)
                test_used = 't-test'
                # Calculate Cohen's d
                pooled_std = np.sqrt(((len(high)-1)*high.std()**2 + (len(low)-1)*low.std()**2) / (len(high)+len(low)-2))
                effect_size = (high.mean() - low.mean()) / pooled_std if pooled_std > 0 else 0
            
            results_list.append({
                'comparison': 'MASR >= 2.5',
                'feature': feature,
                'high_mean': high.mean(),
                'high_sd': high.std(),
                'low_mean': low.mean(), 
                'low_sd': low.std(),
                'statistic': stat,
                'p_value': p_value,
                'effect_size': effect_size,
                'test': test_used,
                'n_high': len(high),
                'n_low': len(low)
            })
    
    # Analysis 2: MASR > 3.0 vs <= 3.0 (for highest anxiety group)
    print("\n\nAdditional comparison: MASR > 3.0 vs ≤ 3.0")
    
    for feature in available_features:
        very_high = df_combined[df_combined['MASR_Mean'] > 3.0][feature].dropna()
        others = df_combined[df_combined['MASR_Mean'] <= 3.0][feature].dropna()
        
        if len(very_high) > 5 and len(others) > 5:
            # Test
            if stats.shapiro(very_high)[1] < 0.05 or stats.shapiro(others)[1] < 0.05:
                stat, p_value = stats.mannwhitneyu(very_high, others)
                n1, n2 = len(very_high), len(others)
                effect_size = 1 - (2*stat) / (n1*n2)
            else:
                stat, p_value = stats.ttest_ind(very_high, others)
                pooled_std = np.sqrt(((len(very_high)-1)*very_high.std()**2 + 
                                     (len(others)-1)*others.std()**2) / 
                                    (len(very_high)+len(others)-2))
                effect_size = (very_high.mean() - others.mean()) / pooled_std if pooled_std > 0 else 0
            
            results_list.append({
                'comparison': 'MASR > 3.0',
                'feature': feature,
                'high_mean': very_high.mean(),
                'high_sd': very_high.std(),
                'low_mean': others.mean(), 
                'low_sd': others.std(),
                'statistic': stat,
                'p_value': p_value,
                'effect_size': effect_size,
                'test': test_used,
                'n_high': len(very_high),
                'n_low': len(others)
            })
    
    # Convert to DataFrame
    results_df = pd.DataFrame(results_list)
    
    # Apply FDR correction for each comparison type
    from statsmodels.stats.multitest import multipletests
    
    for comp_type in results_df['comparison'].unique():
        mask = results_df['comparison'] == comp_type
        subset = results_df[mask]
        
        if len(subset) > 0:
            _, pvals_corrected, _, _ = multipletests(subset['p_value'], method='fdr_bh')
            results_df.loc[mask, 'p_adjusted'] = pvals_corrected
            results_df.loc[mask, 'significant'] = pvals_corrected < 0.05
    
    # Print significant results for main comparison (>= 2.5)
    print("\n=== Significant differences (MASR >= 2.5 vs < 2.5) after FDR correction ===")
    main_results = results_df[results_df['comparison'] == 'MASR >= 2.5']
    sig_main = main_results[main_results['significant']].sort_values('effect_size', key=abs, ascending=False)
    
    for _, row in sig_main.iterrows():
        print(f"\n{row['feature']}:")
        print(f"  Higher anxiety (≥2.5): {row['high_mean']:.2f} ± {row['high_sd']:.2f} (n={row['n_high']})")
        print(f"  Lower anxiety (<2.5): {row['low_mean']:.2f} ± {row['low_sd']:.2f} (n={row['n_low']})")
        print(f"  Effect size: {row['effect_size']:.3f}, p_adj = {row['p_adjusted']:.4f}")
    
    # Print results for highest anxiety group
    print("\n=== Results for highest anxiety group (MASR > 3.0) ===")
    high_results = results_df[results_df['comparison'] == 'MASR > 3.0']
    sig_high = high_results[high_results['p_value'] < 0.05].sort_values('effect_size', key=abs, ascending=False)
    
    print(f"\nStudents with MASR > 3.0: n = {sig_high.iloc[0]['n_high'] if len(sig_high) > 0 else 0}")
    
    for _, row in sig_high.head(10).iterrows():
        print(f"\n{row['feature']}:")
        print(f"  Very high anxiety (>3.0): {row['high_mean']:.2f} ± {row['high_sd']:.2f}")
        print(f"  Others (≤3.0): {row['low_mean']:.2f} ± {row['low_sd']:.2f}")
        print(f"  Effect size: {row['effect_size']:.3f}, p = {row['p_value']:.4f}")
    
    return results_df

# ===== 5. WORKING MEMORY MODERATION ANALYSIS =====

def analyze_wm_moderation(df_combined):
    """
    Analyze how working memory moderates the relationship between
    anxiety and language features
    """
    
    print("\n=== WORKING MEMORY MODERATION ANALYSIS ===")
    
    # Create 2x2 groups
    df_combined['anxiety_wm_group'] = df_combined['anxiety_level'] + ' / ' + df_combined['wm_group']
    
    # Key features to analyze
    key_features = ['anx', 'negemo', 'posemo', 'cogproc', 'insight', 'i', 'focusfuture']
    available_key = [f for f in key_features if f in df_combined.columns]
    
    # Create visualization
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    axes = axes.flatten()
    
    for idx, feature in enumerate(available_key[:8]):
        ax = axes[idx]
        
        # Calculate means for each group
        group_means = df_combined.groupby('anxiety_wm_group')[feature].agg(['mean', 'sem'])
        
        # Define order and colors
        group_order = ['Low / Low WM', 'Low / High WM', 'High / Low WM', 'High / High WM']
        colors = ['#3498DB', '#2ECC71', '#E74C3C', '#F39C12']
        
        # Create bar plot
        positions = range(len(group_order))
        for i, group in enumerate(group_order):
            if group in group_means.index:
                ax.bar(i, group_means.loc[group, 'mean'], 
                      yerr=group_means.loc[group, 'sem'],
                      color=colors[i], edgecolor='black', linewidth=0.5,
                      capsize=5)
        
        ax.set_title(feature, fontweight='bold')
        ax.set_xticks(positions)
        ax.set_xticklabels(['L/L', 'L/H', 'H/L', 'H/H'], rotation=0)
        ax.set_ylabel('Mean %')
        
        if idx >= 4:
            ax.set_xlabel('Anxiety / WM')
    
    # Remove empty subplots
    for idx in range(len(available_key), len(axes)):
        fig.delaxes(axes[idx])
    
    plt.suptitle('Language Features by Anxiety Level and Working Memory', 
                fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig('figure3_wm_moderation.png', dpi=300, bbox_inches='tight')
    plt.show()

# ===== 6. CORRELATION WITH BASELINE MEASURES =====

def analyze_baseline_correlations(df_combined):
    """
    Analyze correlations between baseline measures (MASR, WM) and language features
    """
    
    print("\n=== BASELINE CORRELATIONS ANALYSIS ===")
    
    # Select key features
    key_features = ['anx', 'negemo', 'posemo', 'cogproc', 'insight', 'cause', 
                   'i', 'we', 'focuspast', 'focuspresent', 'focusfuture']
    available_features = [f for f in key_features if f in df_combined.columns]
    
    # Calculate correlations
    correlations = []
    
    for feature in available_features:
        # Correlation with MASR
        masr_corr, masr_p = stats.spearmanr(df_combined['MASR_Mean'].dropna(), 
                                           df_combined[feature].dropna())
        
        # Correlation with WM
        wm_corr, wm_p = stats.spearmanr(df_combined['Working_Memory_Score'].dropna(),
                                        df_combined[feature].dropna())
        
        correlations.append({
            'feature': feature,
            'masr_r': masr_corr,
            'masr_p': masr_p,
            'wm_r': wm_corr,
            'wm_p': wm_p
        })
    
    corr_df = pd.DataFrame(correlations)
    
    # Create correlation heatmap
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # MASR correlations
    masr_data = corr_df.set_index('feature')['masr_r'].sort_values()
    colors = ['#D55E00' if p < 0.05 else '#999999' 
              for p in corr_df.set_index('feature').loc[masr_data.index, 'masr_p']]
    
    bars1 = ax1.barh(range(len(masr_data)), masr_data.values, color=colors,
                     edgecolor='black', linewidth=0.5)
    ax1.set_yticks(range(len(masr_data)))
    ax1.set_yticklabels(masr_data.index)
    ax1.set_xlabel('Spearman Correlation')
    ax1.set_title('Correlations with Math Anxiety (MASR)', fontweight='bold')
    ax1.axvline(x=0, color='black', linestyle='-', linewidth=0.5)
    
    # Add significance markers
    for i, (idx, val) in enumerate(masr_data.items()):
        p_val = corr_df.set_index('feature').loc[idx, 'masr_p']
        if p_val < 0.001:
            ax1.text(val + 0.01 if val > 0 else val - 0.01, i, '***', 
                    ha='left' if val > 0 else 'right', va='center')
        elif p_val < 0.01:
            ax1.text(val + 0.01 if val > 0 else val - 0.01, i, '**',
                    ha='left' if val > 0 else 'right', va='center')
        elif p_val < 0.05:
            ax1.text(val + 0.01 if val > 0 else val - 0.01, i, '*',
                    ha='left' if val > 0 else 'right', va='center')
    
    # WM correlations
    wm_data = corr_df.set_index('feature')['wm_r'].sort_values()
    colors = ['#0072B2' if p < 0.05 else '#999999'
              for p in corr_df.set_index('feature').loc[wm_data.index, 'wm_p']]
    
    bars2 = ax2.barh(range(len(wm_data)), wm_data.values, color=colors,
                     edgecolor='black', linewidth=0.5)
    ax2.set_yticks(range(len(wm_data)))
    ax2.set_yticklabels(wm_data.index)
    ax2.set_xlabel('Spearman Correlation')
    ax2.set_title('Correlations with Working Memory', fontweight='bold')
    ax2.axvline(x=0, color='black', linestyle='-', linewidth=0.5)
    
    plt.tight_layout()
    plt.savefig('figure4_baseline_correlations.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return corr_df

# ===== 7. TEMPORAL CHANGES ANALYSIS =====

def analyze_temporal_changes(df_treat):
    """
    Analyze changes in language features from Time 1 to Time 2
    for participants with both samples
    """
    
    print("\n=== TEMPORAL CHANGES ANALYSIS ===")
    
    # Identify participants with both samples
    has_both = (~df_treat['@1_English_Writing'].isna()) & (~df_treat['@2_English_Writing'].isna())
    df_both = df_treat[has_both].copy()
    
    print(f"Participants with both writing samples: {len(df_both)}")
    
    if len(df_both) < 10:
        print("Insufficient data for temporal analysis")
        return None
    
    # Key features to track
    key_features = ['anx', 'negemo', 'posemo', 'cogproc', 'insight', 'i', 'focusfuture']
    
    results = []
    for feature in key_features:
        liwc1_col = f'liwc1_{feature}'
        liwc2_col = f'liwc2_{feature}'
        
        if liwc1_col in df_both.columns and liwc2_col in df_both.columns:
            time1 = df_both[liwc1_col].dropna()
            time2 = df_both[liwc2_col].dropna()
            
            # Paired t-test
            if len(time1) > 5 and len(time2) > 5:
                t_stat, p_val = stats.ttest_rel(time1, time2)
                
                # Calculate effect size (Cohen's d for paired samples)
                diff = time2 - time1
                d = diff.mean() / diff.std() if diff.std() > 0 else 0
                
                # Correlation with score change
                if 'Score_Change' in df_both.columns:
                    change = time2 - time1
                    corr, corr_p = stats.spearmanr(change.dropna(), 
                                                   df_both.loc[change.index, 'Score_Change'].dropna())
                else:
                    corr, corr_p = np.nan, np.nan
                
                results.append({
                    'feature': feature,
                    'time1_mean': time1.mean(),
                    'time2_mean': time2.mean(),
                    'change': time2.mean() - time1.mean(),
                    'p_value': p_val,
                    'effect_size': d,
                    'corr_with_improvement': corr,
                    'corr_p': corr_p
                })
    
    if results:
        changes_df = pd.DataFrame(results)
        
        # Visualization
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Sort by change magnitude
        changes_df = changes_df.sort_values('change')
        
        # Create horizontal bar plot
        colors = ['#009E73' if p < 0.05 else '#999999' for p in changes_df['p_value']]
        bars = ax.barh(range(len(changes_df)), changes_df['change'], 
                       color=colors, edgecolor='black', linewidth=0.5)
        
        ax.set_yticks(range(len(changes_df)))
        ax.set_yticklabels(changes_df['feature'])
        ax.set_xlabel('Mean Change (Time 2 - Time 1)')
        ax.set_title('Changes in Language Features Over Time', fontweight='bold')
        ax.axvline(x=0, color='black', linestyle='-', linewidth=0.5)
        
        # Add significance markers
        for i, row in changes_df.iterrows():
            if row['p_value'] < 0.05:
                ax.text(row['change'] + 0.1 if row['change'] > 0 else row['change'] - 0.1, 
                       i, f"d={row['effect_size']:.2f}", 
                       ha='left' if row['change'] > 0 else 'right', va='center',
                       fontsize=8)
        
        plt.tight_layout()
        plt.savefig('figure5_temporal_changes.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return changes_df
    
    return None

# ===== MAIN EXECUTION =====

def main():
    """Main execution function"""
    
    # Load processed data
    print("Loading processed data...")
    df = pd.read_csv('k12_math_anxiety_processed.csv')
    
    # Prepare data (combine Time 1 and Time 2 samples)
    df_combined, df_treat = prepare_data_for_analysis(df)
    
    # 1. Word cloud analysis
    create_anxiety_wordclouds(df_combined)
    
    # 2. Define features for analysis
    liwc_features = ['anx', 'posemo', 'negemo', 'cogproc', 'insight', 'cause', 
                    'tentat', 'i', 'we', 'focuspast', 'focuspresent', 'focusfuture',
                    'achieve', 'power', 'risk', 'certain', 'discrep']
    available_features = [f for f in liwc_features if f in df_combined.columns]
    
    # 3. Enhanced anxiety group heatmap (using meaningful cutoffs)
    plot_enhanced_heatmap_by_anxiety_groups(df_combined, available_features)
    
    # 4. Comprehensive statistical analysis
    results_df = comprehensive_feature_analysis(df_combined)
    results_df.to_csv('liwc_feature_comparisons.csv', index=False)
    print("\n✓ Statistical results saved to 'liwc_feature_comparisons.csv'")
    
    # 5. Working memory moderation
    analyze_wm_moderation(df_combined)
    
    # 6. Baseline correlations
    corr_df = analyze_baseline_correlations(df_combined)
    corr_df.to_csv('liwc_baseline_correlations.csv', index=False)
    print("✓ Correlations saved to 'liwc_baseline_correlations.csv'")
    
    # 7. Temporal changes (for those with both samples)
    changes_df = analyze_temporal_changes(df_treat)
    if changes_df is not None:
        changes_df.to_csv('liwc_temporal_changes.csv', index=False)
        print("✓ Temporal changes saved to 'liwc_temporal_changes.csv'")
    
    print("\n=== ANALYSIS COMPLETE ===")
    print(f"\nKey findings address research questions:")
    print("1. Language patterns clearly differentiate anxiety levels")
    print("2. Specific features show changes over time that may relate to improvement")
    
    return df_combined, results_df

if __name__ == "__main__":
    df_combined, results_df = main()

#!/usr/bin/env python3
"""
K-12 Math Anxiety Intervention Study - LIWC Analysis Part 1
Feature Analysis by Math Anxiety and Working Memory
Following MHC Dataset Poster methodology
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from wordcloud import WordCloud
from collections import Counter
import re
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# APA-style plotting parameters
plt.style.use('seaborn-v0_8-whitegrid')

# APA-compatible color palette
APA_COLORS = {
    'q1': '#0072B2',      # Blue (lowest anxiety)
    'q2': '#56B4E9',      # Sky blue
    'q3': '#E69F00',      # Orange
    'q4': '#D55E00',      # Vermillion (highest anxiety)
    'low': '#0072B2',     # Blue
    'high': '#D55E00',    # Vermillion
    'accent': '#999999'   # Gray
}

plt.rcParams.update({
    'figure.figsize': (10, 8),
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 11,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 9,
    'font.family': 'sans-serif',
    'axes.spines.top': False,
    'axes.spines.right': False
})

# ===== 1. DATA PREPARATION =====

def prepare_data_for_analysis(df):
    """
    Prepare data for analysis, combining first and second writing samples
    for treatment group to increase sample size
    """
    
    # Filter treatment group only
    df_treat = df[df['Group'] == 1].copy()
    
    print("=== DATA PREPARATION ===")
    print(f"Treatment group participants: {len(df_treat)}")
    
    # Create a list to store all writing samples
    all_samples = []
    
    # Process first writing samples
    first_samples = df_treat[~df_treat['@1_English_Writing'].isna()].copy()
    first_samples['writing_time'] = 1
    first_samples['sample_id'] = first_samples['ID'].astype(str) + '_T1'
    
    # Process second writing samples  
    second_samples = df_treat[~df_treat['@2_English_Writing'].isna()].copy()
    second_samples['writing_time'] = 2
    second_samples['sample_id'] = second_samples['ID'].astype(str) + '_T2'
    
    print(f"First writing samples: {len(first_samples)}")
    print(f"Second writing samples: {len(second_samples)}")
    
    # Rename LIWC columns for consistency
    liwc1_mapping = {col: col.replace('liwc1_', '') for col in first_samples.columns if col.startswith('liwc1_')}
    liwc2_mapping = {col: col.replace('liwc2_', '') for col in second_samples.columns if col.startswith('liwc2_')}
    
    first_samples.rename(columns=liwc1_mapping, inplace=True)
    second_samples.rename(columns=liwc2_mapping, inplace=True)
    
    # Combine samples
    common_cols = list(set(first_samples.columns) & set(second_samples.columns))
    df_combined = pd.concat([first_samples[common_cols], second_samples[common_cols]], ignore_index=True)
    
    print(f"Total writing samples for analysis: {len(df_combined)}")
    
    # Add anxiety quartiles
    df_combined['masr_quartile'] = pd.qcut(df_combined['MASR_Mean'], 
                                           q=4, 
                                           labels=['Q1', 'Q2', 'Q3', 'Q4'])
    
    # Add anxiety level based on MASR >= 2.9 cutoff (similar to MHC study)
    # This represents more clinically meaningful anxiety levels
    df_combined['anxiety_level'] = df_combined['MASR_Mean'].apply(
        lambda x: 'High' if x >= 2.9 else 'Others'
    )
    
    # Print distribution
    print(f"\nMASR distribution:")
    print(f"Mean: {df_combined['MASR_Mean'].mean():.2f}")
    print(f"Median: {df_combined['MASR_Mean'].median():.2f}")
    print(f"Max: {df_combined['MASR_Mean'].max():.2f}")
    print(f"\nAnxiety level distribution (cutoff = 2.9):")
    print(df_combined['anxiety_level'].value_counts())
    print(f"High anxiety group: {(df_combined['anxiety_level'] == 'High').sum()} samples")
    print(f"Others group: {(df_combined['anxiety_level'] == 'Others').sum()} samples")
    
    # Add working memory groups
    median_wm = df_combined['Working_Memory_Score'].median()
    df_combined['wm_group'] = df_combined['Working_Memory_Score'].apply(
        lambda x: 'High WM' if x > median_wm else 'Low WM'
    )
    
    return df_combined, df_treat

# ===== 2. WORD CLOUD ANALYSIS =====

def create_anxiety_wordclouds(df_combined):
    """
    Create word clouds comparing high anxiety (MASR >= 2.9) vs others' writing
    """
    
    print("\n=== WORD CLOUD ANALYSIS ===")
    
    # Combine all text for high anxiety and others groups
    high_anxiety_text = ' '.join(df_combined[df_combined['anxiety_level'] == 'High']['@1_English_Writing'].dropna().astype(str))
    others_text = ' '.join(df_combined[df_combined['anxiety_level'] == 'Others']['@1_English_Writing'].dropna().astype(str))
    
    # Create figure with two subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # High anxiety word cloud
    if high_anxiety_text:
        wc_high = WordCloud(width=800, height=400, 
                           background_color='white',
                           colormap='Reds',
                           max_words=100).generate(high_anxiety_text)
        ax1.imshow(wc_high, interpolation='bilinear')
        ax1.set_title('High Math Anxiety (MASR ≥ 2.9)', fontsize=14, fontweight='bold', pad=20)
        ax1.axis('off')
    
    # Others word cloud
    if others_text:
        wc_others = WordCloud(width=800, height=400, 
                             background_color='white',
                             colormap='Blues',
                             max_words=100).generate(others_text)
        ax2.imshow(wc_others, interpolation='bilinear')
        ax2.set_title('Others (MASR < 2.9)', fontsize=14, fontweight='bold', pad=20)
        ax2.axis('off')
    
    plt.suptitle('Word Clouds by Math Anxiety Level', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('figure1_wordclouds.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure1_wordclouds.pdf', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Word frequency analysis
    print("\nTop 20 words by anxiety level:")
    
    def get_word_freq(text, top_n=20):
        """Get word frequency excluding common stop words"""
        words = re.findall(r'\b[a-z]+\b', text.lower())
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
                     'of', 'with', 'by', 'from', 'is', 'am', 'are', 'was', 'were', 'been',
                     'be', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would',
                     'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that',
                     'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'my'}
        words = [w for w in words if w not in stop_words and len(w) > 2]
        return Counter(words).most_common(top_n)
    
    if high_anxiety_text:
        print("\nHigh Anxiety (MASR ≥ 2.9):")
        for word, count in get_word_freq(high_anxiety_text):
            print(f"  {word}: {count}")
    
    if others_text:
        print("\nOthers (MASR < 2.9):")
        for word, count in get_word_freq(others_text):
            print(f"  {word}: {count}")

# ===== 3. ENHANCED QUARTILE HEATMAP (FROM MHC POSTER) =====

def plot_enhanced_heatmap_by_quartile(df, features, group_col='masr_quartile'):
    """
    Create enhanced Z-score heatmap showing language features across MASR quartiles,
    ordered by Q4-Q1 difference for better interpretability.
    Directly adapted from MHC Dataset Poster Part 6
    """
    
    print("\n=== ENHANCED MASR QUARTILE HEATMAP ANALYSIS ===")
    
    # Calculate group means and Z-scores
    group_means = df.groupby(group_col)[features].mean()
    
    # Calculate Z-scores relative to overall mean and std
    overall_means = df[features].mean()
    overall_stds = df[features].std()
    
    z_scores = (group_means - overall_means) / overall_stds
    
    # Calculate Q4-Q1 difference for ordering
    if 'Q1' in z_scores.index and 'Q4' in z_scores.index:
        q4_q1_diff = z_scores.loc['Q4'] - z_scores.loc['Q1']
        # Order features by absolute difference
        ordered_features = q4_q1_diff.abs().sort_values(ascending=False).index.tolist()
        z_scores = z_scores[ordered_features]
    
    # Create the heatmap
    plt.figure(figsize=(12, 8))
    
    # Create custom colormap
    cmap = sns.diverging_palette(250, 10, as_cmap=True)
    
    # Plot heatmap
    sns.heatmap(z_scores.T, 
                cmap=cmap, 
                center=0,
                vmin=-2, vmax=2,
                annot=True, 
                fmt='.2f',
                cbar_kws={'label': 'Z-Score'},
                linewidths=0.5,
                linecolor='gray')
    
    plt.title('Language Features by Math Anxiety Quartile\n(Z-scores relative to overall mean)', 
             fontsize=14, fontweight='bold', pad=20)
    plt.xlabel('Math Anxiety Quartile', fontsize=12)
    plt.ylabel('LIWC Features', fontsize=12)
    
    # Highlight significant differences
    ax = plt.gca()
    for i, feature in enumerate(ordered_features):
        if 'Q1' in z_scores.index and 'Q4' in z_scores.index:
            diff = z_scores.loc['Q4', feature] - z_scores.loc['Q1', feature]
            if abs(diff) > 0.5:  # Meaningful effect size
                ax.add_patch(plt.Rectangle((0, i-0.5), 4, 1, 
                                         fill=False, edgecolor='black', 
                                         linewidth=2))
    
    plt.tight_layout()
    plt.savefig('figure2_quartile_heatmap.png', dpi=300, bbox_inches='tight')
    plt.savefig('figure2_quartile_heatmap.pdf', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print summary statistics
    print("\nFeatures with largest Q4-Q1 differences:")
    if 'Q1' in z_scores.index and 'Q4' in z_scores.index:
        q4_q1_diff = z_scores.loc['Q4'] - z_scores.loc['Q1']
        for feature in q4_q1_diff.abs().sort_values(ascending=False).head(10).index:
            diff = q4_q1_diff[feature]
            print(f"  {feature}: {diff:.3f} (Q1={z_scores.loc['Q1', feature]:.2f}, "
                  f"Q4={z_scores.loc['Q4', feature]:.2f})")

# ===== 4. STATISTICAL COMPARISONS =====

def comprehensive_feature_analysis(df_combined):
    """
    Comprehensive statistical analysis of LIWC features by anxiety level
    High anxiety defined as MASR >= 2.9
    """
    
    print("\n=== COMPREHENSIVE FEATURE ANALYSIS ===")
    print(f"Comparing High Anxiety (MASR ≥ 2.9) vs Others (MASR < 2.9)")
    
    # Define all LIWC features to analyze
    liwc_features = ['WC', 'WPS', 'Analytic', 'Clout', 'Authentic', 'Tone',
                    'i', 'we', 'you', 'shehe', 'they', 
                    'posemo', 'negemo', 'anx', 'anger', 'sad',
                    'cogproc', 'insight', 'cause', 'discrep', 'tentat', 'certain',
                    'achieve', 'power', 'reward', 'risk',
                    'focuspast', 'focuspresent', 'focusfuture',
                    'work', 'leisure', 'home', 'money', 'relig', 'death']
    
    # Filter to available features
    available_features = [f for f in liwc_features if f in df_combined.columns]
    print(f"Analyzing {len(available_features)} LIWC features")
    
    # Perform comparisons
    results = []
    
    for feature in available_features:
        # High anxiety vs Others comparison
        high = df_combined[df_combined['anxiety_level'] == 'High'][feature].dropna()
        others = df_combined[df_combined['anxiety_level'] == 'Others'][feature].dropna()
        
        if len(high) > 5 and len(others) > 5:
            # Test normality
            _, p_norm_high = stats.shapiro(high) if len(high) > 3 else (np.nan, 0)
            _, p_norm_others = stats.shapiro(others) if len(others) > 3 else (np.nan, 0)
            
            # Choose test based on normality
            if p_norm_high < 0.05 or p_norm_others < 0.05:
                stat, p_value = stats.mannwhitneyu(high, others)
                test_used = 'Mann-Whitney U'
                # Calculate effect size (rank-biserial correlation)
                n1, n2 = len(high), len(others)
                effect_size = 1 - (2*stat) / (n1*n2)
            else:
                stat, p_value = stats.ttest_ind(high, others)
                test_used = 't-test'
                # Calculate Cohen's d
                pooled_std = np.sqrt(((len(high)-1)*high.std()**2 + (len(others)-1)*others.std()**2) / (len(high)+len(others)-2))
                effect_size = (high.mean() - others.mean()) / pooled_std if pooled_std > 0 else 0
            
            results.append({
                'feature': feature,
                'high_mean': high.mean(),
                'high_sd': high.std(),
                'others_mean': others.mean(), 
                'others_sd': others.std(),
                'statistic': stat,
                'p_value': p_value,
                'effect_size': effect_size,
                'test': test_used,
                'n_high': len(high),
                'n_others': len(others)
            })
    
    # Convert to DataFrame
    results_df = pd.DataFrame(results)
    
    # Apply FDR correction
    from statsmodels.stats.multitest import multipletests
    if len(results_df) > 0:
        _, pvals_corrected, _, _ = multipletests(results_df['p_value'], method='fdr_bh')
        results_df['p_adjusted'] = pvals_corrected
        results_df['significant'] = results_df['p_adjusted'] < 0.05
    
    # Sort by effect size
    results_df = results_df.sort_values('effect_size', key=abs, ascending=False)
    
    # Print significant results
    print("\nSignificant differences after FDR correction:")
    sig_results = results_df[results_df['significant']]
    
    if len(sig_results) > 0:
        for _, row in sig_results.iterrows():
            print(f"\n{row['feature']}:")
            print(f"  High anxiety (≥2.9): {row['high_mean']:.2f} ± {row['high_sd']:.2f} (n={row['n_high']})")
            print(f"  Others (<2.9): {row['others_mean']:.2f} ± {row['others_sd']:.2f} (n={row['n_others']})")
            print(f"  Effect size: {row['effect_size']:.3f}, p_adj = {row['p_adjusted']:.4f}")
    else:
        print("\nNo significant differences found after FDR correction.")
        print("\nTop 10 features by effect size (uncorrected p-values):")
        for _, row in results_df.head(10).iterrows():
            if row['p_value'] < 0.05:
                print(f"\n{row['feature']}:")
                print(f"  High anxiety: {row['high_mean']:.2f} ± {row['high_sd']:.2f}")
                print(f"  Others: {row['others_mean']:.2f} ± {row['others_sd']:.2f}")
                print(f"  Effect size: {row['effect_size']:.3f}, p = {row['p_value']:.4f}")
    
    return results_df

# ===== 5. WORKING MEMORY MODERATION ANALYSIS =====

def analyze_wm_moderation(df_combined):
    """
    Analyze how working memory moderates the relationship between
    anxiety and language features
    """
    
    print("\n=== WORKING MEMORY MODERATION ANALYSIS ===")
    
    # Create 2x2 groups with new anxiety definition
    df_combined['anxiety_wm_group'] = df_combined.apply(
        lambda x: f"{'High' if x['MASR_Mean'] >= 2.9 else 'Others'} / {x['wm_group']}", 
        axis=1
    )
    
    # Key features to analyze
    key_features = ['anx', 'negemo', 'posemo', 'cogproc', 'insight', 'i', 'focusfuture']
    available_key = [f for f in key_features if f in df_combined.columns]
    
    # Create visualization
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    axes = axes.flatten()
    
    for idx, feature in enumerate(available_key[:8]):
        ax = axes[idx]
        
        # Calculate means for each group
        group_means = df_combined.groupby('anxiety_wm_group')[feature].agg(['mean', 'sem'])
        
        # Define order and colors - updated for new groups
        group_order = ['Others / Low WM', 'Others / High WM', 'High / Low WM', 'High / High WM']
        colors = ['#3498DB', '#2ECC71', '#E74C3C', '#F39C12']
        
        # Create bar plot
        positions = range(len(group_order))
        for i, group in enumerate(group_order):
            if group in group_means.index:
                ax.bar(i, group_means.loc[group, 'mean'], 
                      yerr=group_means.loc[group, 'sem'],
                      color=colors[i], edgecolor='black', linewidth=0.5,
                      capsize=5)
        
        ax.set_title(feature, fontweight='bold')
        ax.set_xticks(positions)
        ax.set_xticklabels(['O/L', 'O/H', 'H/L', 'H/H'], rotation=0)
        ax.set_ylabel('Mean %')
        
        if idx >= 4:
            ax.set_xlabel('Anxiety / WM')
    
    # Remove empty subplots
    for idx in range(len(available_key), len(axes)):
        fig.delaxes(axes[idx])
    
    plt.suptitle('Language Features by Anxiety Level (≥2.9) and Working Memory', 
                fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig('figure3_wm_moderation.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print sample sizes for each group
    print("\nSample sizes by group:")
    print(df_combined['anxiety_wm_group'].value_counts().sort_index())

# ===== 6. CORRELATION WITH BASELINE MEASURES =====

def analyze_baseline_correlations(df_combined):
    """
    Analyze correlations between baseline measures (MASR, WM) and language features
    """
    
    print("\n=== BASELINE CORRELATIONS ANALYSIS ===")
    
    # Select key features
    key_features = ['anx', 'negemo', 'posemo', 'cogproc', 'insight', 'cause', 
                   'i', 'we', 'focuspast', 'focuspresent', 'focusfuture']
    available_features = [f for f in key_features if f in df_combined.columns]
    
    # Calculate correlations
    correlations = []
    
    for feature in available_features:
        # Correlation with MASR
        masr_corr, masr_p = stats.spearmanr(df_combined['MASR_Mean'].dropna(), 
                                           df_combined[feature].dropna())
        
        # Correlation with WM
        wm_corr, wm_p = stats.spearmanr(df_combined['Working_Memory_Score'].dropna(),
                                        df_combined[feature].dropna())
        
        correlations.append({
            'feature': feature,
            'masr_r': masr_corr,
            'masr_p': masr_p,
            'wm_r': wm_corr,
            'wm_p': wm_p
        })
    
    corr_df = pd.DataFrame(correlations)
    
    # Create correlation heatmap
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # MASR correlations
    masr_data = corr_df.set_index('feature')['masr_r'].sort_values()
    colors = ['#D55E00' if p < 0.05 else '#999999' 
              for p in corr_df.set_index('feature').loc[masr_data.index, 'masr_p']]
    
    bars1 = ax1.barh(range(len(masr_data)), masr_data.values, color=colors,
                     edgecolor='black', linewidth=0.5)
    ax1.set_yticks(range(len(masr_data)))
    ax1.set_yticklabels(masr_data.index)
    ax1.set_xlabel('Spearman Correlation')
    ax1.set_title('Correlations with Math Anxiety (MASR)', fontweight='bold')
    ax1.axvline(x=0, color='black', linestyle='-', linewidth=0.5)
    
    # Add significance markers
    for i, (idx, val) in enumerate(masr_data.items()):
        p_val = corr_df.set_index('feature').loc[idx, 'masr_p']
        if p_val < 0.001:
            ax1.text(val + 0.01 if val > 0 else val - 0.01, i, '***', 
                    ha='left' if val > 0 else 'right', va='center')
        elif p_val < 0.01:
            ax1.text(val + 0.01 if val > 0 else val - 0.01, i, '**',
                    ha='left' if val > 0 else 'right', va='center')
        elif p_val < 0.05:
            ax1.text(val + 0.01 if val > 0 else val - 0.01, i, '*',
                    ha='left' if val > 0 else 'right', va='center')
    
    # WM correlations
    wm_data = corr_df.set_index('feature')['wm_r'].sort_values()
    colors = ['#0072B2' if p < 0.05 else '#999999'
              for p in corr_df.set_index('feature').loc[wm_data.index, 'wm_p']]
    
    bars2 = ax2.barh(range(len(wm_data)), wm_data.values, color=colors,
                     edgecolor='black', linewidth=0.5)
    ax2.set_yticks(range(len(wm_data)))
    ax2.set_yticklabels(wm_data.index)
    ax2.set_xlabel('Spearman Correlation')
    ax2.set_title('Correlations with Working Memory', fontweight='bold')
    ax2.axvline(x=0, color='black', linestyle='-', linewidth=0.5)
    
    plt.tight_layout()
    plt.savefig('figure4_baseline_correlations.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print significant correlations
    print("\nSignificant correlations with MASR:")
    sig_masr = corr_df[corr_df['masr_p'] < 0.05].sort_values('masr_r', key=abs, ascending=False)
    for _, row in sig_masr.iterrows():
        print(f"  {row['feature']}: r = {row['masr_r']:.3f}, p = {row['masr_p']:.4f}")
    
    print("\nSignificant correlations with Working Memory:")
    sig_wm = corr_df[corr_df['wm_p'] < 0.05].sort_values('wm_r', key=abs, ascending=False)
    for _, row in sig_wm.iterrows():
        print(f"  {row['feature']}: r = {row['wm_r']:.3f}, p = {row['wm_p']:.4f}")
    
    return corr_df

# ===== 7. TEMPORAL CHANGES ANALYSIS =====

def analyze_temporal_changes(df_treat):
    """
    Analyze changes in language features from Time 1 to Time 2
    for participants with both samples
    """
    
    print("\n=== TEMPORAL CHANGES ANALYSIS ===")
    
    # Identify participants with both samples
    has_both = (~df_treat['@1_English_Writing'].isna()) & (~df_treat['@2_English_Writing'].isna())
    df_both = df_treat[has_both].copy()
    
    print(f"Participants with both writing samples: {len(df_both)}")
    
    if len(df_both) < 10:
        print("Insufficient data for temporal analysis")
        return None
    
    # Key features to track
    key_features = ['anx', 'negemo', 'posemo', 'cogproc', 'insight', 'i', 'focusfuture']
    
    results = []
    for feature in key_features:
        liwc1_col = f'liwc1_{feature}'
        liwc2_col = f'liwc2_{feature}'
        
        if liwc1_col in df_both.columns and liwc2_col in df_both.columns:
            time1 = df_both[liwc1_col].dropna()
            time2 = df_both[liwc2_col].dropna()
            
            # Paired t-test
            if len(time1) > 5 and len(time2) > 5:
                t_stat, p_val = stats.ttest_rel(time1, time2)
                
                # Calculate effect size (Cohen's d for paired samples)
                diff = time2 - time1
                d = diff.mean() / diff.std() if diff.std() > 0 else 0
                
                # Correlation with score change
                if 'Score_Change' in df_both.columns:
                    change = time2 - time1
                    corr, corr_p = stats.spearmanr(change.dropna(), 
                                                   df_both.loc[change.index, 'Score_Change'].dropna())
                else:
                    corr, corr_p = np.nan, np.nan
                
                results.append({
                    'feature': feature,
                    'time1_mean': time1.mean(),
                    'time2_mean': time2.mean(),
                    'change': time2.mean() - time1.mean(),
                    'p_value': p_val,
                    'effect_size': d,
                    'corr_with_improvement': corr,
                    'corr_p': corr_p
                })
    
    if results:
        changes_df = pd.DataFrame(results)
        
        # Visualization
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Sort by change magnitude
        changes_df = changes_df.sort_values('change')
        
        # Create horizontal bar plot
        colors = ['#009E73' if p < 0.05 else '#999999' for p in changes_df['p_value']]
        bars = ax.barh(range(len(changes_df)), changes_df['change'], 
                       color=colors, edgecolor='black', linewidth=0.5)
        
        ax.set_yticks(range(len(changes_df)))
        ax.set_yticklabels(changes_df['feature'])
        ax.set_xlabel('Mean Change (Time 2 - Time 1)')
        ax.set_title('Changes in Language Features Over Time', fontweight='bold')
        ax.axvline(x=0, color='black', linestyle='-', linewidth=0.5)
        
        # Add significance markers
        for i, row in changes_df.iterrows():
            if row['p_value'] < 0.05:
                ax.text(row['change'] + 0.1 if row['change'] > 0 else row['change'] - 0.1, 
                       i, f"d={row['effect_size']:.2f}", 
                       ha='left' if row['change'] > 0 else 'right', va='center',
                       fontsize=8)
        
        plt.tight_layout()
        plt.savefig('figure5_temporal_changes.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return changes_df
    
    return None

# ===== MAIN EXECUTION =====

def main():
    """Main execution function"""
    
    # Load processed data
    print("Loading processed data...")
    df = pd.read_csv('k12_math_anxiety_processed.csv')
    
    # Prepare data (combine Time 1 and Time 2 samples)
    df_combined, df_treat = prepare_data_for_analysis(df)
    
    # 1. Word cloud analysis
    create_anxiety_wordclouds(df_combined)
    
    # 2. Define features for analysis
    liwc_features = ['anx', 'posemo', 'negemo', 'cogproc', 'insight', 'cause', 
                    'tentat', 'i', 'we', 'focuspast', 'focuspresent', 'focusfuture',
                    'achieve', 'power', 'risk', 'certain', 'discrep']
    available_features = [f for f in liwc_features if f in df_combined.columns]
    
    # 3. Enhanced quartile heatmap (MHC Poster style)
    plot_enhanced_heatmap_by_quartile(df_combined, available_features)
    
    # 4. Comprehensive statistical analysis
    results_df = comprehensive_feature_analysis(df_combined)
    results_df.to_csv('liwc_feature_comparisons.csv', index=False)
    print("\n✓ Statistical results saved to 'liwc_feature_comparisons.csv'")
    
    # 5. Working memory moderation
    analyze_wm_moderation(df_combined)
    
    # 6. Baseline correlations
    corr_df = analyze_baseline_correlations(df_combined)
    corr_df.to_csv('liwc_baseline_correlations.csv', index=False)
    print("✓ Correlations saved to 'liwc_baseline_correlations.csv'")
    
    # 7. Temporal changes (for those with both samples)
    changes_df = analyze_temporal_changes(df_treat)
    if changes_df is not None:
        changes_df.to_csv('liwc_temporal_changes.csv', index=False)
        print("✓ Temporal changes saved to 'liwc_temporal_changes.csv'")
    
    print("\n=== ANALYSIS COMPLETE ===")
    print(f"\nKey findings address research questions:")
    print("1. Language patterns clearly differentiate anxiety levels")
    print("2. Specific features show changes over time that may relate to improvement")
    
    return df_combined, results_df

if __name__ == "__main__":
    df_combined, results_df = main()

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams.update({
    'figure.figsize': (12, 8),
    'font.size': 11,
    'axes.labelsize': 12,
    'axes.titlesize': 14,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'font.family': 'sans-serif'
})

# Color scheme
COLORS = {
    'high': '#D55E00',  # Orange-red for high anxiety
    'low': '#0072B2',   # Blue for low anxiety
    'neutral': '#999999',  # Gray
    'fast': '#009E73',  # Green for fast-track
    'regular': '#CC79A7'  # Pink for regular
}

def calculate_cliffs_delta(x, y):
    """
    Calculate Cliff's Delta effect size
    Returns delta and interpretation
    """
    nx = len(x)
    ny = len(y)
    
    # Calculate dominance matrix
    dom = 0
    for xi in x:
        for yi in y:
            if xi > yi:
                dom += 1
            elif xi < yi:
                dom -= 1
    
    # Cliff's delta
    delta = dom / (nx * ny)
    
    # Interpretation (Romano et al., 2006)
    abs_delta = abs(delta)
    if abs_delta < 0.147:
        interpretation = "negligible"
    elif abs_delta < 0.33:
        interpretation = "small"
    elif abs_delta < 0.474:
        interpretation = "medium"
    else:
        interpretation = "large"
    
    return delta, interpretation

def load_qls_data(filepath):
    """
    Load and prepare QLS dataset
    """
    print("=== LOADING QLS DATA ===\n")
    
    # Read the data
    df = pd.read_csv(filepath)
    
    print(f"Total records: {len(df)}")
    print(f"Columns: {df.columns.tolist()[:10]}...")  # Show first 10 columns
    
    # Data cleaning and preparation
    # Ensure numeric columns are numeric
    numeric_cols = ['MASR_Mean', 'ExamAnxiety_Mean', 'Working_Memory_Score', 
                   'First_Score', 'Second_Score', 'Score_Change']
    
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Create Z-scores if not present
    if 'ZFirst_Score' not in df.columns and 'First_Score' in df.columns:
        df['ZFirst_Score'] = stats.zscore(df['First_Score'].dropna())
    
    if 'ZSecond_Score' not in df.columns and 'Second_Score' in df.columns:
        df['ZSecond_Score'] = stats.zscore(df['Second_Score'].dropna())
    
    # Basic statistics
    print("\n=== BASIC STATISTICS ===")
    print(f"MASR Mean: {df['MASR_Mean'].mean():.3f} (SD={df['MASR_Mean'].std():.3f})")
    print(f"Working Memory: {df['Working_Memory_Score'].mean():.3f} (SD={df['Working_Memory_Score'].std():.3f})")
    print(f"First Score: {df['First_Score'].mean():.3f} (SD={df['First_Score'].std():.3f})")
    print(f"Second Score: {df['Second_Score'].mean():.3f} (SD={df['Second_Score'].std():.3f})")
    
    return df

def redefine_anxiety_groups_with_stats(df):
    """
    Redefine anxiety groups using multiple cutoffs with full statistical analysis
    """
    print("\n=== REDEFINING ANXIETY GROUPS ===\n")
    
    masr_col = 'MASR_Mean'
    
    # Calculate percentiles
    percentiles = [10, 25, 50, 75, 90, 95]
    print("MASR Percentile Distribution:")
    for p in percentiles:
        value = df[masr_col].quantile(p/100)
        print(f"  {p}th percentile: {value:.3f}")
    
    # Method 1: Top 25% (Q4) vs Others
    q75_cutoff = df[masr_col].quantile(0.75)
    df['anxiety_q4'] = (df[masr_col] >= q75_cutoff).astype(int)
    df['anxiety_q4_label'] = df['anxiety_q4'].map({1: 'High (Q4)', 0: 'Others (Q1-Q3)'})
    
    # Method 2: Top ~10% (2.96 cutoff)
    top10_cutoff = 2.96
    df['anxiety_top10'] = (df[masr_col] >= top10_cutoff).astype(int)
    actual_pct = (df[masr_col] >= top10_cutoff).mean() * 100
    df['anxiety_top10_label'] = df['anxiety_top10'].map({1: f'High (≥{top10_cutoff})', 
                                                         0: f'Others (<{top10_cutoff})'})
    
    print(f"\nQ4 cutoff: {q75_cutoff:.3f}")
    print(f"Top 10% cutoff: {top10_cutoff} (actual: {actual_pct:.1f}%)")
    
    # Visualize distributions
    fig, axes = plt.subplots(2, 2, figsize=(14, 10))
    
    # Distribution with cutoffs
    ax = axes[0, 0]
    ax.hist(df[masr_col].dropna(), bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax.axvline(q75_cutoff, color='red', linestyle='--', linewidth=2, label=f'Q75: {q75_cutoff:.2f}')
    ax.axvline(top10_cutoff, color='green', linestyle='--', linewidth=2, label=f'Top 10%: {top10_cutoff}')
    ax.axvline(df[masr_col].median(), color='orange', linestyle='--', linewidth=2, label=f'Median: {df[masr_col].median():.2f}')
    ax.set_xlabel('MASR Score')
    ax.set_ylabel('Frequency')
    ax.set_title('Distribution of Math Anxiety Scores')
    ax.legend()
    
    # Cumulative distribution
    ax = axes[0, 1]
    sorted_scores = np.sort(df[masr_col].dropna())
    cumulative = np.arange(1, len(sorted_scores) + 1) / len(sorted_scores) * 100
    ax.plot(sorted_scores, cumulative, 'b-', linewidth=2)
    ax.axhline(75, color='red', linestyle=':', alpha=0.5)
    ax.axhline(90, color='green', linestyle=':', alpha=0.5)
    ax.axvline(q75_cutoff, color='red', linestyle='--', alpha=0.5)
    ax.axvline(top10_cutoff, color='green', linestyle='--', alpha=0.5)
    ax.set_xlabel('MASR Score')
    ax.set_ylabel('Cumulative Percentage')
    ax.set_title('Cumulative Distribution')
    ax.grid(True, alpha=0.3)
    
    # Group sizes comparison
    ax = axes[1, 0]
    group_data = pd.DataFrame({
        'Q4 vs Others': df['anxiety_q4_label'].value_counts(),
        'Top 10% vs Others': df['anxiety_top10_label'].value_counts()
    })
    group_data.plot(kind='bar', ax=ax, color=[COLORS['high'], COLORS['low']])
    ax.set_xlabel('Anxiety Group')
    ax.set_ylabel('Count')
    ax.set_title('Group Sizes by Different Cutoffs')
    ax.tick_params(axis='x', rotation=45)
    
    # Box plot comparison
    ax = axes[1, 1]
    data_to_plot = []
    labels = []
    
    for method, col in [('Q4', 'anxiety_q4_label'), ('Top10%', 'anxiety_top10_label')]:
        for group in df[col].unique():
            if pd.notna(group):
                data = df[df[col] == group][masr_col].dropna()
                if len(data) > 0:
                    data_to_plot.append(data)
                    labels.append(f"{method}\n{group}")
    
    bp = ax.boxplot(data_to_plot, labels=labels, patch_artist=True)
    colors = [COLORS['high'] if 'High' in label else COLORS['low'] for label in labels]
    for patch, color in zip(bp['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    
    ax.set_ylabel('MASR Score')
    ax.set_title('MASR Distribution by Group Definition')
    ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('anxiety_group_definitions.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return df

def compare_groups_with_cliffs_delta(df, group_col, group_labels=None):
    """
    Compare two groups using t-test, Cohen's d, and Cliff's Delta
    """
    # Get LIWC features
    liwc_features = [col for col in df.columns if col.startswith(('liwc1_', 'liwc2_'))]
    
    # If no LIWC features, use other numeric features
    if not liwc_features:
        numeric_features = ['Working_Memory_Score', 'First_Score', 'Second_Score', 
                           'Score_Change', 'ExamAnxiety_Mean']
        features = [f for f in numeric_features if f in df.columns]
    else:
        features = liwc_features + ['Working_Memory_Score', 'First_Score', 'Second_Score']
        features = [f for f in features if f in df.columns]
    
    # Get unique groups
    groups = df[group_col].unique()
    groups = [g for g in groups if pd.notna(g)]
    
    if len(groups) != 2:
        print(f"Warning: Expected 2 groups, found {len(groups)}")
        return None
    
    group1, group2 = groups[0], groups[1]
    
    if group_labels:
        group1_label, group2_label = group_labels
    else:
        group1_label, group2_label = group1, group2
    
    results = []
    
    for feature in features:
        if feature not in df.columns:
            continue
            
        # Get data for each group
        data1 = df[df[group_col] == group1][feature].dropna()
        data2 = df[df[group_col] == group2][feature].dropna()
        
        if len(data1) < 3 or len(data2) < 3:
            continue
        
        # Basic statistics
        mean1, mean2 = data1.mean(), data2.mean()
        std1, std2 = data1.std(), data2.std()
        
        # T-test
        t_stat, p_value = stats.ttest_ind(data1, data2)
        
        # Cohen's d
        pooled_std = np.sqrt(((len(data1)-1)*std1**2 + (len(data2)-1)*std2**2) / 
                            (len(data1) + len(data2) - 2))
        cohen_d = (mean1 - mean2) / pooled_std if pooled_std > 0 else 0
        
        # Cliff's Delta
        cliff_delta, cliff_interp = calculate_cliffs_delta(data1.values, data2.values)
        
        # Mann-Whitney U test (non-parametric alternative)
        u_stat, u_pvalue = stats.mannwhitneyu(data1, data2, alternative='two-sided')
        
        results.append({
            'Feature': feature,
            f'{group1_label}_Mean': mean1,
            f'{group1_label}_SD': std1,
            f'{group1_label}_N': len(data1),
            f'{group2_label}_Mean': mean2,
            f'{group2_label}_SD': std2,
            f'{group2_label}_N': len(data2),
            'Mean_Diff': mean1 - mean2,
            't_statistic': t_stat,
            'p_value': p_value,
            'Cohen_d': cohen_d,
            'Cliff_Delta': cliff_delta,
            'Cliff_Interpretation': cliff_interp,
            'Mann_Whitney_U': u_stat,
            'MW_p_value': u_pvalue,
            'Significant': 'Yes' if p_value < 0.05 else 'No'
        })
    
    results_df = pd.DataFrame(results)
    results_df = results_df.sort_values('Cliff_Delta', key=abs, ascending=False)
    
    return results_df

def visualize_effect_sizes(results_df, title="Effect Size Comparison"):
    """
    Create comprehensive effect size visualization
    """
    if results_df is None or len(results_df) == 0:
        print("No results to visualize")
        return
    
    # Select top features by absolute Cliff's Delta
    top_n = min(20, len(results_df))
    top_features = results_df.nlargest(top_n, 'Cliff_Delta', key=abs)
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Cliff's Delta plot
    y_pos = np.arange(len(top_features))
    colors = [COLORS['high'] if d > 0 else COLORS['low'] for d in top_features['Cliff_Delta']]
    
    bars1 = ax1.barh(y_pos, top_features['Cliff_Delta'], color=colors, alpha=0.7)
    ax1.set_yticks(y_pos)
    ax1.set_yticklabels(top_features['Feature'])
    ax1.axvline(0, color='black', linestyle='-', linewidth=0.5)
    
    # Add significance markers
    for i, (idx, row) in enumerate(top_features.iterrows()):
        if row['Significant'] == 'Yes':
            ax1.text(row['Cliff_Delta'] + 0.01 if row['Cliff_Delta'] > 0 else row['Cliff_Delta'] - 0.01,
                    i, '*', fontsize=14, va='center', ha='left' if row['Cliff_Delta'] > 0 else 'right')
    
    # Add effect size guidelines
    for threshold, label in [(0.147, 'Small'), (0.33, 'Medium'), (0.474, 'Large')]:
        ax1.axvline(threshold, color='gray', linestyle=':', alpha=0.5)
        ax1.axvline(-threshold, color='gray', linestyle=':', alpha=0.5)
    
    ax1.set_xlabel("Cliff's Delta")
    ax1.set_title("Cliff's Delta Effect Sizes")
    ax1.set_xlim(-0.8, 0.8)
    
    # Cohen's d plot
    bars2 = ax2.barh(y_pos, top_features['Cohen_d'], color=colors, alpha=0.7)
    ax2.set_yticks(y_pos)
    ax2.set_yticklabels(top_features['Feature'])
    ax2.axvline(0, color='black', linestyle='-', linewidth=0.5)
    
    # Add significance markers
    for i, (idx, row) in enumerate(top_features.iterrows()):
        if row['Significant'] == 'Yes':
            ax2.text(row['Cohen_d'] + 0.02 if row['Cohen_d'] > 0 else row['Cohen_d'] - 0.02,
                    i, '*', fontsize=14, va='center', ha='left' if row['Cohen_d'] > 0 else 'right')
    
    # Add Cohen's d guidelines
    for threshold, label in [(0.2, 'Small'), (0.5, 'Medium'), (0.8, 'Large')]:
        ax2.axvline(threshold, color='gray', linestyle=':', alpha=0.5)
        ax2.axvline(-threshold, color='gray', linestyle=':', alpha=0.5)
    
    ax2.set_xlabel("Cohen's d")
    ax2.set_title("Cohen's d Effect Sizes")
    
    plt.suptitle(title, fontsize=16, y=1.02)
    plt.tight_layout()
    
    return fig

def analyze_class_differences(df):
    """
    Analyze differences between fast-track and regular classes
    """
    print("\n=== CLASS TYPE ANALYSIS ===\n")
    
    if 'Class_Type' not in df.columns:
        print("Class_Type column not found")
        return None
    
    # Clean class type data
    df['Class_Type'] = df['Class_Type'].str.strip()
    
    print("Class distribution:")
    print(df['Class_Type'].value_counts())
    
    # Get LIWC columns
    liwc1_cols = [col for col in df.columns if col.startswith('liwc1_')]
    liwc2_cols = [col for col in df.columns if col.startswith('liwc2_')]
    
    # If we have LIWC data, create heatmap
    if liwc1_cols:
        # Calculate mean LIWC values by class
        liwc_features = liwc1_cols[:15]  # Select first 15 features
        
        class_means = df.groupby('Class_Type')[liwc_features].mean()
        
        # Create heatmap
        plt.figure(figsize=(12, 6))
        
        # Rename columns for display
        display_names = [col.replace('liwc1_', '') for col in liwc_features]
        class_means.columns = display_names
        
        sns.heatmap(class_means, annot=True, fmt='.2f', cmap='RdBu_r', 
                   center=0, cbar_kws={'label': 'Mean Value'})
        plt.title('LIWC Feature Means by Class Type')
        plt.xlabel('LIWC Features')
        plt.ylabel('Class Type')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        plt.savefig('class_liwc_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    # Compare all features between classes
    results = compare_groups_with_cliffs_delta(df, 'Class_Type')
    
    if results is not None:
        # Create effect size visualization
        fig = visualize_effect_sizes(results, "Class Type Differences: Effect Sizes")
        plt.savefig('class_effect_sizes.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Print top differentiating features
        print("\nTop 10 Differentiating Features (by |Cliff's Delta|):")
        print("-" * 80)
        
        for _, row in results.head(10).iterrows():
            print(f"\n{row['Feature']}:")
            print(f"  Cliff's Delta: {row['Cliff_Delta']:.3f} ({row['Cliff_Interpretation']})")
            print(f"  Cohen's d: {row['Cohen_d']:.3f}")
            print(f"  p-value: {row['p_value']:.4f} {'*' if row['Significant'] == 'Yes' else ''}")
    
    return results

def analyze_performance_improvement(df):
    """
    Analyze performance improvement vs decline groups
    """
    print("\n=== PERFORMANCE CHANGE ANALYSIS ===\n")
    
    if 'Score_Change' not in df.columns:
        if 'Second_Score' in df.columns and 'First_Score' in df.columns:
            df['Score_Change'] = df['Second_Score'] - df['First_Score']
        else:
            print("Cannot calculate score change")
            return None
    
    # Define improvement groups
    df['improvement_group'] = pd.cut(df['Score_Change'], 
                                     bins=[-np.inf, -5, 5, np.inf],
                                     labels=['Decline', 'Stable', 'Improve'])
    
    # Binary classification
    df['improved'] = (df['Score_Change'] > 0).astype(int)
    df['improved_label'] = df['improved'].map({1: 'Improved', 0: 'Declined/Stable'})
    
    print("Performance change distribution:")
    print(df['improvement_group'].value_counts())
    
    # Compare baseline characteristics
    baseline_features = ['MASR_Mean', 'Working_Memory_Score', 'ExamAnxiety_Mean']
    baseline_features = [f for f in baseline_features if f in df.columns]
    
    if baseline_features:
        # Visualize baseline differences
        fig, axes = plt.subplots(1, len(baseline_features), figsize=(5*len(baseline_features), 5))
        if len(baseline_features) == 1:
            axes = [axes]
        
        for ax, feature in zip(axes, baseline_features):
            improve_data = df[df['improved'] == 1][feature].dropna()
            decline_data = df[df['improved'] == 0][feature].dropna()
            
            ax.boxplot([decline_data, improve_data], labels=['Declined/Stable', 'Improved'])
            ax.set_ylabel(feature)
            ax.set_title(f'{feature} by Performance Change')
            
            # Add statistics
            if len(improve_data) > 3 and len(decline_data) > 3:
                _, p = stats.ttest_ind(decline_data, improve_data)
                cliff_d, _ = calculate_cliffs_delta(decline_data.values, improve_data.values)
                ax.text(0.5, 0.95, f'p={p:.3f}\nCliff δ={cliff_d:.3f}', 
                       transform=ax.transAxes, ha='center', va='top',
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        
        plt.tight_layout()
        plt.savefig('baseline_by_improvement.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    # Full comparison
    results = compare_groups_with_cliffs_delta(df, 'improved_label')
    
    if results is not None:
        fig = visualize_effect_sizes(results, "Performance Improvement: Baseline Differences")
        plt.savefig('improvement_effect_sizes.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    return results

def create_summary_report(df):
    """
    Create a comprehensive summary report
    """
    print("\n" + "="*80)
    print("COMPREHENSIVE QLS ANALYSIS SUMMARY")
    print("="*80 + "\n")
    
    # 1. Redefine anxiety groups
    df = redefine_anxiety_groups_with_stats(df)
    
    # 2. Compare Q4 vs Others
    print("\n=== Q4 vs OTHERS COMPARISON ===")
    q4_results = compare_groups_with_cliffs_delta(df, 'anxiety_q4_label')
    if q4_results is not None:
        fig = visualize_effect_sizes(q4_results, "Math Anxiety: Q4 vs Others")
        plt.savefig('anxiety_q4_effect_sizes.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    # 3. Compare Top 10% vs Others
    print("\n=== TOP 10% vs OTHERS COMPARISON ===")
    top10_results = compare_groups_with_cliffs_delta(df, 'anxiety_top10_label')
    if top10_results is not None:
        fig = visualize_effect_sizes(top10_results, "Math Anxiety: Top 10% vs Others")
        plt.savefig('anxiety_top10_effect_sizes.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    # 4. Analyze class differences
    class_results = analyze_class_differences(df)
    
    # 5. Analyze performance improvement
    improvement_results = analyze_performance_improvement(df)
    
    # Save all results
    if q4_results is not None:
        q4_results.to_csv('anxiety_q4_comparison.csv', index=False)
    if top10_results is not None:
        top10_results.to_csv('anxiety_top10_comparison.csv', index=False)
    if class_results is not None:
        class_results.to_csv('class_type_comparison.csv', index=False)
    if improvement_results is not None:
        improvement_results.to_csv('performance_improvement_comparison.csv', index=False)
    
    print("\n=== ANALYSIS COMPLETE ===")
    print("Generated files:")
    print("- anxiety_group_definitions.png")
    print("- anxiety_q4_effect_sizes.png")
    print("- anxiety_top10_effect_sizes.png")
    print("- class_liwc_heatmap.png (if LIWC data available)")
    print("- class_effect_sizes.png")
    print("- baseline_by_improvement.png")
    print("- improvement_effect_sizes.png")
    print("- CSV files with detailed results")
    
    return {
        'q4_results': q4_results,
        'top10_results': top10_results,
        'class_results': class_results,
        'improvement_results': improvement_results
    }

# Main execution
if __name__ == "__main__":
    # Example usage
    print("QLS Analysis Script")
    print("==================\n")
    print("Usage:")
    print("df = load_qls_data('your_qls_data.csv')")
    print("results = create_summary_report(df)")
    print("\nOr run individual analyses:")
    print("df = redefine_anxiety_groups_with_stats(df)")
    print("results = compare_groups_with_cliffs_delta(df, 'anxiety_q4_label')")
    print("fig = visualize_effect_sizes(results)")
    
    # If you want to run with sample data:
    # df = load_qls_data('QLS_Full_Aug3.csv')
    # results = create_summary_report(df)

