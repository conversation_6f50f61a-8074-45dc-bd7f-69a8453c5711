#!/usr/bin/env python3
"""
NARST 2026 Analysis Pipeline
Math Anxiety and Expressive Writing: A Text Analytics Approach
Focus: K-12 International School Sample
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import spearmanr
import statsmodels.api as sm
from statsmodels.stats.multitest import multipletests
from statsmodels.regression.linear_model import OLS
from statsmodels.stats.sandwich_covariance import cov_hc3
import warnings
warnings.filterwarnings('ignore')

# Set publication-quality defaults
plt.rcParams['font.size'] = 10
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['figure.figsize'] = (6, 4)
sns.set_style("whitegrid")
sns.set_palette("colorblind")

def load_and_clean_data(filepath='QLS_SPSS_Aug8.csv'):
    """Load and preprocess main data with consistent NA handling"""
    df = pd.read_csv(filepath)
    
    # Handle various NA representations
    na_values = ['#NULL!', '#N/A', 'NA', 'nan', '']
    df = df.replace(na_values, np.nan)
    
    # Convert numeric columns
    numeric_cols = ['ZFirst_Score', 'ZSecond_Score', 'MASR_Mean', 
                   'Working_Memory_Score', 'Score_Change']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Standardize gender coding (0=Female, 1=Male)
    if 'Gender' in df.columns:
        df['Gender01'] = df['Gender'].map({
            'Female': 0, 'F': 0, 0: 0, 1: 0,  # Female = 0
            'Male': 1, 'M': 1, 1: 1, 2: 1     # Male = 1
        })
    
    # Ensure Group coding (0=Control, 1=Intervention)
    if 'Intervention or Control' in df.columns:
        df['Group'] = (df['Intervention or Control'] == 'Intervention').astype(int)
    elif 'Group' in df.columns:
        df['Group'] = pd.to_numeric(df['Group'], errors='coerce')
    
    # Class type standardization
    if 'FastRegularClass' in df.columns:
        df['ClassType'] = df['FastRegularClass'].map({'Fast': 1, 'Regular': 0})
    elif 'Class_Type' in df.columns:
        df['ClassType'] = df['Class_Type'].map({'Fast': 1, 'Regular': 0})
    
    return df

def load_and_merge_liwc_data(df):
    """Load and merge LIWC data - FIXED VERSION"""
    try:
        # Load LIWC files
        liwc1 = pd.read_csv('LIWC1_LIWC2015_results.csv')
        liwc2 = pd.read_csv('LIWC2_LIWC2015_results.csv')
        
        # Clean LIWC data
        na_values = ['#NULL!', '#N/A', 'NA', 'nan', '']
        liwc1.replace(na_values, np.nan, inplace=True)
        liwc2.replace(na_values, np.nan, inplace=True)
        
        # Rename LIWC columns with prefix
        liwc1_cols = {c: f'liwc1_{c}' if c != 'ID' else 'ID' for c in liwc1.columns}
        liwc1.rename(columns=liwc1_cols, inplace=True)
        
        liwc2_cols = {c: f'liwc2_{c}' if c != 'ID' else 'ID' for c in liwc2.columns}
        liwc2.rename(columns=liwc2_cols, inplace=True)
        
        # Merge based on ID
        if 'ID' in df.columns:
            df = df.merge(liwc1, on='ID', how='left')
            df = df.merge(liwc2, on='ID', how='left')
        else:
            # Use index as ID if no ID column
            df['ID'] = df.index
            df = df.merge(liwc1, on='ID', how='left')
            df = df.merge(liwc2, on='ID', how='left')
        
        print(f"LIWC data loaded: {len(liwc1)} entries from LIWC1, {len(liwc2)} entries from LIWC2")
        
        # Convert LIWC columns to numeric
        liwc_cols = [c for c in df.columns if c.startswith('liwc')]
        for col in liwc_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        print(f"Total LIWC columns: {len(liwc_cols)}")
        
    except FileNotFoundError as e:
        print(f"Warning: LIWC files not found: {e}")
    except Exception as e:
        print(f"Error loading LIWC data: {e}")
    
    return df

def select_liwc_features(df, time_point='liwc2'):
    """Select LIWC shortlist features - prefer liwc2 (closer to exam)"""
    
    # Target features for NARST analysis
    shortlist = ['negemo', 'anx', 'posemo', 'i', 'cogproc', 
                'insight', 'focusfuture', 'certain', 'cause', 
                'Analytic', 'Tone', 'WC', 'tentat', 'Authentic']
    
    liwc_cols = {}
    
    # Try liwc2 first (closer to exam), then liwc1
    for feature in shortlist:
        col_name = f'{time_point}_{feature}'
        if col_name in df.columns:
            liwc_cols[feature] = col_name
        elif f'liwc1_{feature}' in df.columns:  # Fallback to liwc1
            col_name = f'liwc1_{feature}'
            liwc_cols[feature] = col_name
    
    # Add log-transformed word count
    if f'{time_point}_WC' in df.columns:
        df[f'{time_point}_logWC'] = np.log1p(df[f'{time_point}_WC'])
    elif 'liwc1_WC' in df.columns:
        df['liwc1_logWC'] = np.log1p(df['liwc1_WC'])
    
    print(f"Selected {len(liwc_cols)} LIWC features: {list(liwc_cols.keys())[:10]}...")
    
    return df, liwc_cols

def run_ancova_analysis(df):
    """RQ1 & RQ2: ANCOVA for anxiety-performance and intervention effect"""
    
    # Select complete cases for core variables
    core_vars = ['ZSecond_Score', 'ZFirst_Score', 'MASR_Mean', 
                'Gender01', 'Working_Memory_Score', 'Group']
    df_complete = df[core_vars].dropna()
    
    print(f"Complete cases for ANCOVA: {len(df_complete)}/{len(df)}")
    
    if len(df_complete) < 10:
        print("Warning: Insufficient data for ANCOVA")
        return None, None, None
    
    # Prepare variables
    y = df_complete['ZSecond_Score']
    X = df_complete[['ZFirst_Score', 'MASR_Mean', 'Gender01', 
                     'Working_Memory_Score', 'Group']]
    X = sm.add_constant(X)
    
    # Run main ANCOVA with HC3 robust SE
    model = OLS(y, X).fit()
    robust_model = model.get_robustcov_results(cov_type='HC3')
    
    # Calculate effect sizes
    r_squared = model.rsquared
    adj_r_squared = model.rsquared_adj
    
    # Convert to pandas Series if needed
    if isinstance(robust_model.params, np.ndarray):
        # Create pandas Series with proper index
        param_names = X.columns.tolist()
        params = pd.Series(robust_model.params, index=param_names)
        bse = pd.Series(robust_model.bse, index=param_names)
        pvalues = pd.Series(robust_model.pvalues, index=param_names)
        
        # Confidence intervals
        conf_int_array = robust_model.conf_int()
        conf_int = pd.DataFrame(conf_int_array, index=param_names, columns=['lower', 'upper'])
    else:
        params = robust_model.params
        bse = robust_model.bse
        pvalues = robust_model.pvalues
        conf_int = robust_model.conf_int()
    
    # Extract key results
    results = {
        'n': len(df_complete),
        'r_squared': r_squared,
        'adj_r_squared': adj_r_squared,
        'masr_beta': params['MASR_Mean'] if 'MASR_Mean' in params.index else np.nan,
        'masr_se': bse['MASR_Mean'] if 'MASR_Mean' in bse.index else np.nan,
        'masr_ci': [conf_int.loc['MASR_Mean', 'lower'], conf_int.loc['MASR_Mean', 'upper']] if 'MASR_Mean' in conf_int.index else [np.nan, np.nan],
        'masr_p': pvalues['MASR_Mean'] if 'MASR_Mean' in pvalues.index else np.nan,
        'group_beta': params['Group'] if 'Group' in params.index else np.nan,
        'group_se': bse['Group'] if 'Group' in bse.index else np.nan,
        'group_ci': [conf_int.loc['Group', 'lower'], conf_int.loc['Group', 'upper']] if 'Group' in conf_int.index else [np.nan, np.nan],
        'group_p': pvalues['Group'] if 'Group' in pvalues.index else np.nan
    }
    
    # Create summary table for manuscript
    create_ancova_table_fixed(params, bse, conf_int, pvalues, results)
    
    return df_complete, results, robust_model

def create_ancova_table_fixed(params, bse, conf_int, pvalues, results):
    """Create publication-ready ANCOVA table with fixed indexing"""
    
    table_data = []
    vars = ['const', 'ZFirst_Score', 'MASR_Mean', 'Gender01', 
           'Working_Memory_Score', 'Group']
    var_labels = ['Intercept', 'Baseline Score', 'Math Anxiety', 
                 'Gender (Male)', 'Working Memory', 'Intervention']
    
    for var, label in zip(vars, var_labels):
        if var in params.index:
            beta = params[var]
            se = bse[var]
            ci_low = conf_int.loc[var, 'lower']
            ci_high = conf_int.loc[var, 'upper']
            p = pvalues[var]
            
            # Format p-value
            if p < 0.001:
                p_str = '<.001'
            elif p < 0.01:
                p_str = f'{p:.3f}'
            else:
                p_str = f'{p:.3f}'
            
            table_data.append({
                'Predictor': label,
                'β': f'{beta:.3f}',
                'SE': f'{se:.3f}',
                '95% CI': f'[{ci_low:.3f}, {ci_high:.3f}]',
                'p': p_str
            })
    
    table_df = pd.DataFrame(table_data)
    
    # Add model statistics
    print("\nTable 1. ANCOVA Results (DV: Post-test Score)")
    print("=" * 70)
    print(table_df.to_string(index=False))
    print("-" * 70)
    print(f"N = {results['n']}, R² = {results['r_squared']:.3f}, "
          f"Adj. R² = {results['adj_r_squared']:.3f}")
    print("=" * 70)
    
    return table_df

def analyze_class_profiles(df, liwc_cols, time_point='liwc1'):
    """RQ3: Compare linguistic profiles between Fast and Regular classes"""
    
    # Filter for students with writing samples
    df_writing = df[(df['Group'] == 1) & (df['ClassType'].notna())].copy()
    
    # Prepare for multiple comparisons
    profile_results = []
    
    for feature, col_name in liwc_cols.items():
        if col_name not in df_writing.columns:
            continue
            
        # Get data for each class type
        fast_data = df_writing[df_writing['ClassType'] == 1][col_name].dropna()
        regular_data = df_writing[df_writing['ClassType'] == 0][col_name].dropna()
        
        if len(fast_data) < 5 or len(regular_data) < 5:
            continue
        
        # Run regression with log(WC) control
        df_temp = df_writing[[col_name, 'ClassType', f'{time_point}_logWC']].dropna()
        
        if len(df_temp) > 10:
            X = sm.add_constant(df_temp[['ClassType', f'{time_point}_logWC']])
            y = df_temp[col_name]
            
            model = OLS(y, X).fit()
            robust = model.get_robustcov_results(cov_type='HC3')
            
            # Calculate effect size (Hedges' g)
            pooled_sd = np.sqrt(((len(fast_data)-1)*fast_data.std()**2 + 
                                (len(regular_data)-1)*regular_data.std()**2) / 
                               (len(fast_data) + len(regular_data) - 2))
            hedges_g = (fast_data.mean() - regular_data.mean()) / pooled_sd
            
            profile_results.append({
                'Feature': feature,
                'Fast_Mean': fast_data.mean(),
                'Fast_SD': fast_data.std(),
                'Regular_Mean': regular_data.mean(),
                'Regular_SD': regular_data.std(),
                'Beta': robust.params['ClassType'],
                'SE': robust.bse['ClassType'],
                'p_value': robust.pvalues['ClassType'],
                'Effect_Size': hedges_g
            })
    
    # Apply FDR correction
    profile_df = pd.DataFrame(profile_results)
    if len(profile_df) > 0:
        _, q_values, _, _ = multipletests(profile_df['p_value'], 
                                         method='fdr_bh', alpha=0.05)
        profile_df['q_value'] = q_values
        profile_df['Significant_FDR'] = q_values < 0.05
    
    return profile_df

def create_profile_figure(profile_df):
    """Create Figure 2: Class type linguistic profiles"""
    
    # Select top features by effect size or significance
    plot_features = ['Tone', 'cogproc', 'certain', 'cause', 'posemo',
                    'negemo', 'anx', 'Analytic', 'insight', 'focusfuture']
    
    plot_df = profile_df[profile_df['Feature'].isin(plot_features)].copy()
    plot_df = plot_df.sort_values('Effect_Size', ascending=True)
    
    fig, ax = plt.subplots(figsize=(7, 5))
    
    # Create dumbbell plot
    for i, row in enumerate(plot_df.itertuples()):
        # Draw connecting line
        ax.plot([row.Regular_Mean, row.Fast_Mean], [i, i], 
               'k-', alpha=0.3, linewidth=1)
        
        # Plot points
        ax.scatter(row.Regular_Mean, i, s=100, c='#E69F00', 
                  label='Regular' if i == 0 else "", zorder=3)
        ax.scatter(row.Fast_Mean, i, s=100, c='#0072B2', 
                  label='Fast' if i == 0 else "", zorder=3)
        
        # Add significance marker
        if row.Significant_FDR:
            ax.text(max(row.Regular_Mean, row.Fast_Mean) + 2, i, '*', 
                   fontsize=14, va='center')
    
    # Formatting
    ax.set_yticks(range(len(plot_df)))
    ax.set_yticklabels(plot_df['Feature'])
    ax.set_xlabel('Mean Score', fontsize=11)
    ax.set_title('Figure 2. Linguistic Profiles by Class Type', 
                fontsize=12, fontweight='bold')
    ax.legend(loc='best', frameon=True)
    ax.grid(True, alpha=0.3, axis='x')
    
    plt.tight_layout()
    plt.savefig('figure2_class_profiles.pdf', dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig

def analyze_language_correlations(df, liwc_cols):
    """RQ4: Correlate linguistic features with anxiety and performance"""
    
    correlation_results = []
    
    for feature, col_name in liwc_cols.items():
        # Correlation with post-test score
        valid_data = df[[col_name, 'ZSecond_Score', 'MASR_Mean']].dropna()
        
        if len(valid_data) > 20:
            # Spearman correlations
            r_score, p_score = spearmanr(valid_data[col_name], 
                                        valid_data['ZSecond_Score'])
            r_anxiety, p_anxiety = spearmanr(valid_data[col_name], 
                                            valid_data['MASR_Mean'])
            
            correlation_results.append({
                'Feature': feature,
                'r_Score': r_score,
                'p_Score': p_score,
                'r_Anxiety': r_anxiety,
                'p_Anxiety': p_anxiety,
                'n': len(valid_data)
            })
    
    corr_df = pd.DataFrame(correlation_results)
    
    # Apply FDR correction separately for each outcome
    if len(corr_df) > 0:
        _, q_score, _, _ = multipletests(corr_df['p_Score'], 
                                        method='fdr_bh', alpha=0.05)
        _, q_anxiety, _, _ = multipletests(corr_df['p_Anxiety'], 
                                          method='fdr_bh', alpha=0.05)
        
        corr_df['q_Score'] = q_score
        corr_df['q_Anxiety'] = q_anxiety
        corr_df['Sig_Score_FDR'] = q_score < 0.05
        corr_df['Sig_Anxiety_FDR'] = q_anxiety < 0.05
    
    create_correlation_table(corr_df)
    
    return corr_df

def create_correlation_table(corr_df):
    """Create Table 2: Language-outcome correlations"""
    
    print("\nTable 2. Linguistic Features and Outcomes (Spearman Correlations)")
    print("=" * 80)
    
    # Format for publication
    table_data = []
    for _, row in corr_df.iterrows():
        score_sig = '*' if row['Sig_Score_FDR'] else ''
        anxiety_sig = '*' if row['Sig_Anxiety_FDR'] else ''
        
        table_data.append({
            'Feature': row['Feature'],
            'Post-test Score': f"{row['r_Score']:.3f}{score_sig}",
            'Math Anxiety': f"{row['r_Anxiety']:.3f}{anxiety_sig}"
        })
    
    table_df = pd.DataFrame(table_data)
    print(table_df.to_string(index=False))
    print("-" * 80)
    print("Note: * indicates q < .05 after FDR correction")
    print("=" * 80)
    
    return table_df

def run_mediation_analysis(df, n_bootstrap=5000):
    """RQ5: Gender → Math Anxiety → Performance mediation"""
    
    # Select complete cases
    mediation_vars = ['ZSecond_Score', 'ZFirst_Score', 'MASR_Mean', 
                     'Gender01', 'Working_Memory_Score', 'Group']
    df_med = df[mediation_vars].dropna()
    
    print(f"Mediation analysis N = {len(df_med)}")
    
    # Path a: Gender → Math Anxiety
    X_a = sm.add_constant(df_med[['Gender01', 'ZFirst_Score', 
                                   'Working_Memory_Score', 'Group']])
    y_a = df_med['MASR_Mean']
    model_a = OLS(y_a, X_a).fit()
    robust_a = model_a.get_robustcov_results(cov_type='HC3')
    
    # Path b & c': Math Anxiety & Gender → Performance
    X_bc = sm.add_constant(df_med[['MASR_Mean', 'Gender01', 'ZFirst_Score',
                                    'Working_Memory_Score', 'Group']])
    y_bc = df_med['ZSecond_Score']
    model_bc = OLS(y_bc, X_bc).fit()
    robust_bc = model_bc.get_robustcov_results(cov_type='HC3')
    
    # Extract path coefficients
    a_path = robust_a.params['Gender01']
    a_se = robust_a.bse['Gender01']
    a_p = robust_a.pvalues['Gender01']
    
    b_path = robust_bc.params['MASR_Mean']
    b_se = robust_bc.bse['MASR_Mean']
    b_p = robust_bc.pvalues['MASR_Mean']
    
    c_prime = robust_bc.params['Gender01']
    c_prime_se = robust_bc.bse['Gender01']
    c_prime_p = robust_bc.pvalues['Gender01']
    
    # Bootstrap indirect effect
    indirect_effects = []
    
    for _ in range(n_bootstrap):
        # Resample with replacement
        boot_idx = np.random.choice(len(df_med), len(df_med), replace=True)
        boot_df = df_med.iloc[boot_idx]
        
        try:
            # Bootstrap path a
            X_boot_a = sm.add_constant(boot_df[['Gender01', 'ZFirst_Score',
                                                'Working_Memory_Score', 'Group']])
            y_boot_a = boot_df['MASR_Mean']
            boot_a = OLS(y_boot_a, X_boot_a).fit().params['Gender01']
            
            # Bootstrap path b
            X_boot_bc = sm.add_constant(boot_df[['MASR_Mean', 'Gender01', 'ZFirst_Score',
                                                 'Working_Memory_Score', 'Group']])
            y_boot_bc = boot_df['ZSecond_Score']
            boot_b = OLS(y_boot_bc, X_boot_bc).fit().params['MASR_Mean']
            
            indirect_effects.append(boot_a * boot_b)
        except:
            continue
    
    # Calculate CI for indirect effect
    indirect_effect = a_path * b_path
    ci_lower = np.percentile(indirect_effects, 2.5)
    ci_upper = np.percentile(indirect_effects, 97.5)
    
    # Standardized effects (using standardized variables)
    df_std = df_med.copy()
    for var in ['ZSecond_Score', 'MASR_Mean', 'ZFirst_Score', 'Working_Memory_Score']:
        df_std[f'{var}_std'] = (df_std[var] - df_std[var].mean()) / df_std[var].std()
    
    # Standardized path coefficients
    X_std_a = sm.add_constant(df_std[['Gender01', 'ZFirst_Score_std',
                                       'Working_Memory_Score_std', 'Group']])
    y_std_a = df_std['MASR_Mean_std']
    std_a = OLS(y_std_a, X_std_a).fit().params['Gender01']
    
    X_std_bc = sm.add_constant(df_std[['MASR_Mean_std', 'Gender01', 'ZFirst_Score_std',
                                        'Working_Memory_Score_std', 'Group']])
    y_std_bc = df_std['ZSecond_Score_std']
    model_std_bc = OLS(y_std_bc, X_std_bc).fit()
    std_b = model_std_bc.params['MASR_Mean_std']
    std_c_prime = model_std_bc.params['Gender01']
    
    mediation_results = {
        'a_path': a_path,
        'a_se': a_se,
        'a_p': a_p,
        'a_std': std_a,
        'b_path': b_path,
        'b_se': b_se,
        'b_p': b_p,
        'b_std': std_b,
        'c_prime': c_prime,
        'c_prime_se': c_prime_se,
        'c_prime_p': c_prime_p,
        'c_prime_std': std_c_prime,
        'indirect_effect': indirect_effect,
        'indirect_ci_lower': ci_lower,
        'indirect_ci_upper': ci_upper,
        'indirect_std': std_a * std_b
    }
    
    return mediation_results

def create_mediation_figure(results):
    """Create Figure 3: Mediation path diagram"""
    
    fig, ax = plt.subplots(figsize=(8, 5))
    ax.axis('off')
    
    # Node positions
    pos = {
        'Gender': (0.2, 0.5),
        'Anxiety': (0.5, 0.7),
        'Performance': (0.8, 0.5)
    }
    
    # Draw nodes
    for label, (x, y) in pos.items():
        if label == 'Anxiety':
            color = '#90EE90'  # Light green
        elif label == 'Performance':
            color = '#FFB6C1'  # Light pink
        else:
            color = '#87CEEB'  # Sky blue
            
        circle = plt.Circle((x, y), 0.08, color=color, ec='black', linewidth=2)
        ax.add_patch(circle)
        ax.text(x, y, label, ha='center', va='center', fontsize=11, fontweight='bold')
    
    # Draw paths with coefficients
    # Path a: Gender → Anxiety
    ax.annotate('', xy=(0.42, 0.7), xytext=(0.28, 0.55),
                arrowprops=dict(arrowstyle='->', lw=2, color='black'))
    ax.text(0.32, 0.65, f"a = {results['a_std']:.3f}**", 
            fontsize=10, ha='center')
    
    # Path b: Anxiety → Performance
    ax.annotate('', xy=(0.72, 0.55), xytext=(0.58, 0.65),
                arrowprops=dict(arrowstyle='->', lw=2, color='black'))
    ax.text(0.65, 0.62, f"b = {results['b_std']:.3f}***", 
            fontsize=10, ha='center')
    
    # Path c': Gender → Performance (direct)
    ax.annotate('', xy=(0.72, 0.5), xytext=(0.28, 0.5),
                arrowprops=dict(arrowstyle='->', lw=1, color='gray', linestyle='--'))
    ax.text(0.5, 0.45, f"c' = {results['c_prime_std']:.3f} (ns)", 
            fontsize=10, ha='center', color='gray')
    
    # Add indirect effect text
    indirect_text = (f"Indirect effect (a × b) = {results['indirect_std']:.3f}\n"
                    f"95% CI [{results['indirect_ci_lower']:.3f}, "
                    f"{results['indirect_ci_upper']:.3f}]")
    ax.text(0.5, 0.25, indirect_text, ha='center', fontsize=10,
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    
    # Title
    ax.text(0.5, 0.9, 'Figure 3. Gender → Math Anxiety → Performance Mediation',
            ha='center', fontsize=12, fontweight='bold')
    
    # Note
    ax.text(0.5, 0.1, 'Note: Standardized coefficients shown. **p < .01, ***p < .001',
            ha='center', fontsize=9, style='italic')
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    
    plt.tight_layout()
    plt.savefig('figure3_mediation.pdf', dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig

def create_scatter_figure(df):
    """Create Figure 1: Math Anxiety vs Performance scatter plot"""
    
    # Get complete data for visualization
    plot_df = df[['MASR_Mean', 'ZSecond_Score']].dropna()
    
    # Calculate correlation
    r, p = spearmanr(plot_df['MASR_Mean'], plot_df['ZSecond_Score'])
    
    fig, ax = plt.subplots(figsize=(6, 5))
    
    # Scatter plot with regression line
    ax.scatter(plot_df['MASR_Mean'], plot_df['ZSecond_Score'], 
              alpha=0.5, s=30, color='#4169E1')
    
    # Add regression line
    z = np.polyfit(plot_df['MASR_Mean'], plot_df['ZSecond_Score'], 1)
    p_line = np.poly1d(z)
    x_line = np.linspace(plot_df['MASR_Mean'].min(), 
                        plot_df['MASR_Mean'].max(), 100)
    ax.plot(x_line, p_line(x_line), 'r-', alpha=0.8, linewidth=2)
    
    # Add correlation text
    ax.text(0.95, 0.05, f'r = {r:.3f}***', transform=ax.transAxes,
            fontsize=11, ha='right', va='bottom',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # Labels and title
    ax.set_xlabel('Math Anxiety (MASR)', fontsize=11)
    ax.set_ylabel('Post-test Score (Z-score)', fontsize=11)
    ax.set_title('Figure 1. Math Anxiety and Academic Performance', 
                fontsize=12, fontweight='bold')
    
    # Add note
    fig.text(0.5, 0.02, 'Note: Relationship remains significant after controlling for baseline score and covariates',
             ha='center', fontsize=9, style='italic')
    
    plt.tight_layout()
    plt.savefig('figure1_scatter.pdf', dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig

def run_complete_analysis():
    """Main analysis pipeline for NARST submission"""
    
    print("=" * 80)
    print("NARST 2026 Analysis Pipeline")
    print("Math Anxiety and Expressive Writing: A Text Analytics Approach")
    print("=" * 80)
    
    # Load data - 修改这部分
    print("\n1. Loading and preprocessing data...")
    df = load_and_clean_data()
    df = load_and_merge_liwc_data(df)  # 使用新的LIWC加载函数
    df, liwc_cols = select_liwc_features(df, time_point='liwc2')  # 优先使用liwc2
    print(f"Total sample size: {len(df)}")
    
    # Initialize results dictionary
    all_results = {}
    
    # RQ1 & RQ2: ANCOVA
    print("\n2. RQ1 & RQ2: ANCOVA Analysis...")
    df_complete, ancova_results, ancova_model = run_ancova_analysis(df)
    
    if ancova_results is not None:
        all_results['ancova'] = ancova_results
        
        # Figure 1: Scatter plot
        print("\n3. Creating Figure 1: Anxiety-Performance Relationship...")
        fig1 = create_scatter_figure(df)
    else:
        print("Skipping ANCOVA-based analyses due to insufficient data")
        all_results['ancova'] = {}
    
    # RQ3: Class profiles - 现在应该能正常工作
    print("\n4. RQ3: Analyzing Class Type Linguistic Profiles...")
    if len(liwc_cols) > 0:
        profile_df = analyze_class_profiles(df, liwc_cols)
        if len(profile_df) > 0:
            all_results['profiles'] = profile_df
            fig2 = create_profile_figure(profile_df)
            
            # Report key findings
            sig_features = profile_df[profile_df['Significant_FDR']]
            if len(sig_features) > 0:
                print(f"\nSignificant differences (FDR < .05): {len(sig_features)} features")
                for _, row in sig_features.head(5).iterrows():
                    direction = "higher" if row['Effect_Size'] > 0 else "lower"
                    print(f"  - {row['Feature']}: Fast classes {direction} (d={row['Effect_Size']:.2f})")
    else:
        print("No LIWC features available for class profile analysis")
        all_results['profiles'] = pd.DataFrame()
    
    # RQ4: Language-outcome correlations
    print("\n5. RQ4: Language-Outcome Correlations...")
    if len(liwc_cols) > 0:
        corr_df = analyze_language_correlations(df, liwc_cols)
        all_results['correlations'] = corr_df
        
        # Report if any survive FDR
        if len(corr_df) > 0:
            sig_corrs = corr_df[(corr_df['Sig_Score_FDR']) | (corr_df['Sig_Anxiety_FDR'])]
            if len(sig_corrs) == 0:
                print("Note: No correlations survived FDR correction; effects are small")
                print("Linguistic features may be better suited for diagnostic feedback than prediction")
            else:
                print(f"Significant correlations after FDR: {len(sig_corrs)}")
    else:
        all_results['correlations'] = pd.DataFrame()
    
    # RQ5: Mediation
    print("\n6. RQ5: Gender-Anxiety-Performance Mediation...")
    try:
        mediation_results = run_mediation_analysis(df)
        all_results['mediation'] = mediation_results
        fig3 = create_mediation_figure(mediation_results)
        
        # Print mediation summary
        print(f"\nMediation Results:")
        print(f"Indirect effect: {mediation_results['indirect_effect']:.4f}")
        print(f"95% CI: [{mediation_results['indirect_ci_lower']:.4f}, "
              f"{mediation_results['indirect_ci_upper']:.4f}]")
        if mediation_results['indirect_ci_lower'] > 0:
            print("Significant indirect path: Gender affects performance through math anxiety")
    except Exception as e:
        print(f"Mediation analysis failed: {e}")
        all_results['mediation'] = {}
    
    # Save results
    save_results(all_results)
    
    return all_results

def save_results(results):
    """Save all results to Excel"""
    try:
        with pd.ExcelWriter('narst2026_results.xlsx') as writer:
            # ANCOVA results
            if 'ancova' in results and results['ancova']:
                ancova_summary = pd.DataFrame([results['ancova']])
                ancova_summary.to_excel(writer, sheet_name='ANCOVA', index=False)
            
            # Class profiles
            if 'profiles' in results and len(results['profiles']) > 0:
                results['profiles'].to_excel(writer, sheet_name='Class_Profiles', index=False)
            
            # Correlations
            if 'correlations' in results and len(results['correlations']) > 0:
                results['correlations'].to_excel(writer, sheet_name='Correlations', index=False)
            
            # Mediation
            if 'mediation' in results and results['mediation']:
                med_summary = pd.DataFrame([results['mediation']])
                med_summary.to_excel(writer, sheet_name='Mediation', index=False)
        
        print("\n" + "=" * 80)
        print("Analysis complete. Results saved to narst2026_results.xlsx")
        print("Figures saved as PDF files for manuscript submission")
        print("=" * 80)
    except Exception as e:
        print(f"Error saving results: {e}")

if __name__ == "__main__":
    results = run_complete_analysis()

def generate_results_text(results):
    """Generate manuscript-ready results text"""
    
    text = []
    
    # RQ1 results
    if 'ancova' in results and results['ancova']:
        text.append(f"RQ1: Math anxiety significantly predicted post-test performance "
                    f"(β = {results['ancova']['masr_beta']:.3f}, "
                    f"95% CI [{results['ancova']['masr_ci'][0]:.3f}, "
                    f"{results['ancova']['masr_ci'][1]:.3f}], "
                    f"p = {results['ancova']['masr_p']:.3f}), "
                    f"even after controlling for baseline score and covariates.")
        
        # RQ2 results  
        text.append(f"RQ2: The expressive writing intervention showed no significant "
                    f"effect on post-test scores (β = {results['ancova']['group_beta']:.3f}, "
                    f"95% CI [{results['ancova']['group_ci'][0]:.3f}, "
                    f"{results['ancova']['group_ci'][1]:.3f}], "
                    f"p = {results['ancova']['group_p']:.3f}).")
    
    # RQ5 results
    if 'mediation' in results and results['mediation']:
        med = results['mediation']
        if 'indirect_std' in med and 'c_prime_std' in med and med['c_prime_std'] != 0:
            text.append(f"RQ5: Gender showed a significant indirect effect on performance "
                        f"through math anxiety (indirect effect = {med['indirect_std']:.3f}, "
                        f"95% CI [{med['indirect_ci_lower']:.3f}, "
                        f"{med['indirect_ci_upper']:.3f}]), "
                        f"with the indirect path accounting for a meaningful portion of the relationship.")
    
    return '\n\n'.join(text) if text else "Results text generation requires complete analysis results."

# Run the complete analysis
if __name__ == "__main__":
    results = run_complete_analysis()
    
    # Generate manuscript text
    print("\n" + "=" * 80)
    print("MANUSCRIPT-READY RESULTS TEXT:")
    print("=" * 80)
    if results:
        print(generate_results_text(results))
    else:
        print("Analysis incomplete. Check data and try again.")