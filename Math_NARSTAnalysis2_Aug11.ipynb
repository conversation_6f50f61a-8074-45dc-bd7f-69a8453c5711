#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NARST (Strand 12) – Minimal, publication-ready pipeline
RQs:
  RQ1  Math anxiety vs post-test (controlling baseline)
  RQ2  Adjusted effect of expressive writing (Group)
  RQ3  Class-type (Fast vs Regular) language profile differences
  RQ4  Correlations of selected LIWC with Z2 and MASR (FDR)
  RQ5  Mediation: Gender → MASR → Z2 (covariate-adjusted)

Notes:
  - Figures: APA7 style, saved as PDF (vector) + PNG
  - Tables: Excel workbook with compact sheets
  - Robust SE: HC3
  - FDR: <PERSON><PERSON><PERSON> (BH)
  - Keep prints lean; no verbose tutorials.
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
import statsmodels.api as sm
from statsmodels.stats.multitest import multipletests

# ----------------------------
# Config
# ----------------------------
NA_VALUES = ['#NULL!', '#N/A', '#VALUE!', '#DIV/0!', 'NULL', 'null', 'N/A', 'NA', 'NaN', '']
DATA_FILE = 'QLS_SPSS_Aug8.csv'
LIWC_FILE = 'LIWC1_LIWC2015_results.csv'  # merged if available
OUT_XLSX = 'narst_results.xlsx'
FIG_DIR = 'figures_narst'
os.makedirs(FIG_DIR, exist_ok=True)

# Shortlist (keep ≤12). We’ll pick the available ones under the chosen LIWC prefix.
LIWC_SHORTLIST_BASE = [
    'negemo', 'anx', 'posemo', 'i',
    'cogproc', 'insight', 'focusfuture',
    'certain', 'cause', 'Analytic', 'Tone', 'WC'
]


# ----------------------------
# Utilities
# ----------------------------
def set_apa_style():
    plt.rcParams.update({
        'figure.figsize': (6.0, 4.5),
        'font.size': 10,
        'axes.labelsize': 10,
        'axes.titlesize': 11,
        'font.family': 'sans-serif',
        'font.sans-serif': ['Arial', 'DejaVu Sans'],
        'axes.spines.top': False,
        'axes.spines.right': False,
        'axes.grid': True,
        'grid.alpha': 0.25,
        'savefig.dpi': 300
    })


def zscore(a: pd.Series) -> pd.Series:
    a = pd.to_numeric(a, errors='coerce')
    m, s = a.mean(skipna=True), a.std(skipna=True, ddof=1)
    return (a - m) / s if s and np.isfinite(s) else a * np.nan


def fdr_bh(pvals: pd.Series, alpha=0.05) -> pd.Series:
    mask = pvals.notna().values
    q = np.full(len(pvals), np.nan)
    if mask.sum() > 0:
        q[mask] = multipletests(pvals[mask].values, alpha=alpha, method='fdr_bh')[1]
    return pd.Series(q, index=pvals.index)


def stars_from_p(p: float) -> str:
    if p < 0.001: return '***'
    if p < 0.01: return '**'
    if p < 0.05: return '*'
    return ''


def add_const(X: pd.DataFrame) -> pd.DataFrame:
    return sm.add_constant(X, has_constant='add')


def fit_ols_robust(y: pd.Series, X: pd.DataFrame):
    res = sm.OLS(y, add_const(X)).fit()
    return res.get_robustcov_results(cov_type='HC3')


def hedges_g(x, y) -> float:
    # small-sample corrected Cohen's d
    x = pd.to_numeric(pd.Series(x), errors='coerce').dropna()
    y = pd.to_numeric(pd.Series(y), errors='coerce').dropna()
    nx, ny = len(x), len(y)
    if nx < 2 or ny < 2: return np.nan
    sx2, sy2 = x.var(ddof=1), y.var(ddof=1)
    sp = np.sqrt(((nx - 1) * sx2 + (ny - 1) * sy2) / (nx + ny - 2))
    d = (x.mean() - y.mean()) / sp if sp > 0 else np.nan
    J = 1 - 3 / (4 * (nx + ny) - 9)
    return d * J

def coef_by_name(res, term):
    """
    Safely extract coefficient tuple (b, se, t, p) by term name from a statsmodels result,
    regardless of whether params/bse/... are Series or numpy arrays.
    """
    names = getattr(getattr(res, 'model', None), 'exog_names', None)
    if not names or term not in names:
        return np.nan, np.nan, np.nan, np.nan
    j = list(names).index(term)
    params = np.asarray(res.params)
    bse    = np.asarray(res.bse)
    tvals  = np.asarray(res.tvalues)
    pvals  = np.asarray(res.pvalues)
    return params[j], bse[j], tvals[j], pvals[j]

# ----------------------------
# Load & harmonize data
# ----------------------------
def load_data():
    df = pd.read_csv(DATA_FILE, na_values=NA_VALUES)
    df.columns = df.columns.str.strip()
    # Merge LIWC when present (prefix liwc1_)
    if os.path.exists(LIWC_FILE):
        liwc = pd.read_csv(LIWC_FILE, na_values=NA_VALUES)
        liwc.rename(columns={c: (f'liwc1_{c}' if c != 'ID' else c) for c in liwc.columns}, inplace=True)
        if 'ID' in df.columns:
            df = df.merge(liwc, on='ID', how='left')
    # Gender01
    if 'Gender01' not in df.columns and 'Gender' in df.columns:
        mapping = {0: 0, 1: 0, 2: 1, 'Female': 0, 'Male': 1, 'F': 0, 'M': 1}
        df['Gender01'] = df['Gender'].map(mapping)
        if df['Gender01'].isna().any():
            g = df['Gender'].dropna().unique()
            if len(g) == 2:
                df['Gender01'] = df['Gender'].map({min(g): 0, max(g): 1})
    # ClassType (Fast=1, Regular=0) – try common columns
    ct = None
    for cand in ['FastRegularClass', 'Class_Type', 'ClassType']:
        if cand in df.columns:
            ct = cand; break
    if ct:
        # Attempt to coerce into 0/1
        v = df[ct]
        if set(pd.Series(v.dropna().unique()).astype(str)) <= {'0', '1', 'Fast', 'Regular', 'fast', 'regular'}:
            df['ClassType01'] = v.map({'Regular': 0, 'regular': 0, '0': 0, 0: 0,
                                       'Fast': 1, 'fast': 1, '1': 1, 1: 1})
        else:
            # If numeric but not {0,1}, binarize by mode labels (best effort)
            df['ClassType01'] = pd.Categorical(v).codes
            # Renorm to {0,1}
            m = df['ClassType01'].dropna().unique()
            if len(m) == 2:
                low, high = sorted(m)
                df['ClassType01'] = df['ClassType01'].replace({low: 0, high: 1})
    # Group (0/1)
    if 'Group' in df.columns:
        df['Group'] = pd.to_numeric(df['Group'], errors='coerce')
    # LIWC prefix (prefer time closer to exam if available)
    prefix = 'liwc2_'
    if not any(c.startswith('liwc2_') for c in df.columns):
        prefix = 'liwc1_'
    # log word count
    wc_col = f'{prefix}WC'
    if wc_col in df.columns and 'log_wc' not in df.columns:
        df['log_wc'] = np.log1p(pd.to_numeric(df[wc_col], errors='coerce'))
    return df, prefix


def shortlist_liwc(df: pd.DataFrame, prefix: str):
    cols = []
    for k in LIWC_SHORTLIST_BASE:
        cand = f'{prefix}{k}'
        if cand in df.columns:
            cols.append(cand)
    return cols


# ----------------------------
# RQ1 & RQ2 – ANCOVA + Figure 1
# ----------------------------
def analyze_rq1_rq2(df: pd.DataFrame):
    keep = ['ZSecond_Score', 'ZFirst_Score', 'MASR_Mean', 'Gender01', 'Working_Memory_Score', 'Group']
    dat = df[keep].apply(pd.to_numeric, errors='coerce').dropna()
    if len(dat) < 50:
        raise ValueError("Too few complete cases for ANCOVA.")
    # Spearman (MASR vs Z2)
    rs, ps = stats.spearmanr(dat['MASR_Mean'], dat['ZSecond_Score'])
    # ANCOVA
    X = dat[['ZFirst_Score', 'MASR_Mean', 'Gender01', 'Working_Memory_Score', 'Group']]
    y = dat['ZSecond_Score']
    m = fit_ols_robust(y, X)
    # Compact table
    tab = pd.DataFrame({
        'coef': m.params,
        'se': m.bse,
        't': m.tvalues,
        'p': m.pvalues
    })
    # Figure 1: scatter MASR vs Z2
    set_apa_style()
    fig, ax = plt.subplots()
    ax.scatter(dat['MASR_Mean'], dat['ZSecond_Score'], alpha=0.6, s=24)
    # Least-squares line for visualization
    slope, intercept, *_ = stats.linregress(dat['MASR_Mean'], dat['ZSecond_Score'])
    xx = np.linspace(dat['MASR_Mean'].min(), dat['MASR_Mean'].max(), 100)
    ax.plot(xx, intercept + slope * xx, linestyle='--', linewidth=1.5)
    ax.set_xlabel('Math Anxiety (MASR)')
    ax.set_ylabel('Post-test Score (Z)')
    ax.set_title('MASR and Post-test Performance')
    ax.text(0.02, 0.02, f"Spearman r = {rs:.2f}", transform=ax.transAxes)
    fig.tight_layout()
    fig.savefig(os.path.join(FIG_DIR, 'fig1_masr_vs_z2.pdf'))
    fig.savefig(os.path.join(FIG_DIR, 'fig1_masr_vs_z2.png'))
    plt.close(fig)
    return rs, ps, tab


# ----------------------------
# RQ3 – Class-type language profile (Figure 2)
# ----------------------------
def analyze_rq3_class_profile(df: pd.DataFrame, prefix: str, liwc_cols: list):
    if 'ClassType01' not in df.columns:
        return pd.DataFrame(), None
    results = []
    for col in liwc_cols:
        keep = ['ClassType01', 'log_wc', col]
        dat = df[keep].apply(pd.to_numeric, errors='coerce').dropna()
        if len(dat) < 60 or dat['ClassType01'].nunique() < 2:  # guard
            continue
        y = dat[col]
        X = dat[['ClassType01', 'log_wc']] if 'log_wc' in dat.columns else dat[['ClassType01']]
        res = fit_ols_robust(y, X)
        # Collect effects
        # Hedges g for readable effect size
        g = hedges_g(y[dat['ClassType01'] == 1], y[dat['ClassType01'] == 0])
        results.append({
            'feature': col.replace(prefix, ''),
            'n': len(dat),

            'g': g,
            'mean_fast': y[dat['ClassType01'] == 1].mean(),
            'mean_regular': y[dat['ClassType01'] == 0].mean()
        })
    res_df = pd.DataFrame(results)
    if res_df.empty:
        return res_df, None
    res_df['q'] = fdr_bh(res_df['p'])

    # choose Top12
    tmp = res_df.copy()
    tmp['abs_g'] = tmp['g'].abs()
    show = (tmp.sort_values(['q', 'abs_g'], na_position='last')
                .head(12)
                .sort_values('g'))

    # Figure 2: select top by |g| or q
    show = (res_df.sort_values(['q', res_df['g'].abs().name])
            .head(10)
            .sort_values('g'))
    set_apa_style()
    fig, ax = plt.subplots(figsize=(6, 5.5))
    ytick = np.arange(len(show))
    ax.barh(ytick, show['mean_fast'] - show['mean_regular'], alpha=0.85)
    ax.axvline(0, color='gray', linestyle='--', linewidth=1)
    ax.set_yticks(ytick)
    ax.set_yticklabels(show['feature'])
    ax.set_xlabel('Fast – Regular (Mean Difference)')
    ax.set_title('Class-type Language Profile (Fast vs Regular)')
    # FDR stars
    for i, (d, q) in enumerate(zip(show['mean_fast'] - show['mean_regular'], show['q'])):
        s = '***' if q < 0.001 else '**' if q < 0.01 else '*' if q < 0.05 else ''
        if s:
            x = d + (0.02 if d >= 0 else -0.02)
            ha = 'left' if d >= 0 else 'right'
            ax.text(x, i, s, va='center', ha=ha)
    fig.tight_layout()
    fig.savefig(os.path.join(FIG_DIR, 'fig2_class_profile.pdf'))
    fig.savefig(os.path.join(FIG_DIR, 'fig2_class_profile.png'))
    plt.close(fig)
    return res_df, show[['feature', 'mean_fast', 'mean_regular', 'g', 'p', 'q']]


# ----------------------------
# RQ4 – Correlations (Table 2)
# ----------------------------
def analyze_rq4_correlations(df: pd.DataFrame, prefix: str, liwc_cols: list):
    keep = ['ZSecond_Score', 'MASR_Mean'] + liwc_cols
    dat = df[keep].apply(pd.to_numeric, errors='coerce').dropna()
    if len(dat) < 60:
        return pd.DataFrame()
    # Compute Spearman r for each LIWC vs Z2 and vs MASR
    rows = []
    for col in liwc_cols:
        r1, p1 = stats.spearmanr(dat[col], dat['ZSecond_Score'])
        r2, p2 = stats.spearmanr(dat[col], dat['MASR_Mean'])
        rows.append({'feature': col.replace(prefix, ''),
                     'r_LIWC_Z2': r1, 'p_LIWC_Z2': p1,
                     'r_LIWC_MASR': r2, 'p_LIWC_MASR': p2})
    out = pd.DataFrame(rows)
    out['q_Z2'] = fdr_bh(out['p_LIWC_Z2'])
    out['q_MASR'] = fdr_bh(out['p_LIWC_MASR'])
    return out.sort_values('r_LIWC_Z2')


# ----------------------------
# RQ5 – Mediation: Gender → MASR → Z2 (Figure 3)
# ----------------------------
def analyze_rq5_mediation(df: pd.DataFrame):
    keep = ['Gender01', 'MASR_Mean', 'ZFirst_Score', 'ZSecond_Score', 'Working_Memory_Score', 'Group']
    dat = df[keep].apply(pd.to_numeric, errors='coerce').dropna()
    if len(dat) < 80 or dat['Gender01'].nunique() < 2:
        return {}
    # a: MASR ~ Gender + Z1 + WM + Group
    y_a = dat['MASR_Mean']; X_a = dat[['Gender01', 'ZFirst_Score', 'Working_Memory_Score', 'Group']]
    a = fit_ols_robust(y_a, X_a)
    # b/c': Z2 ~ MASR + Gender + Z1 + WM + Group
    y_b = dat['ZSecond_Score']; X_b = dat[['MASR_Mean', 'Gender01', 'ZFirst_Score', 'Working_Memory_Score', 'Group']]
    b = fit_ols_robust(y_b, X_b)
    # standardize for path coefficients (z)
    Z = dat[['MASR_Mean', 'Gender01', 'ZFirst_Score', 'Working_Memory_Score', 'Group', 'ZSecond_Score']].copy()
    for c in Z.columns:
        if c != 'Gender01':  # keep Gender01 as 0/1
            Z[c] = zscore(Z[c])
    a_z = fit_ols_robust(Z['MASR_Mean'], Z[['Gender01', 'ZFirst_Score', 'Working_Memory_Score', 'Group']])
    b_z = fit_ols_robust(Z['ZSecond_Score'], Z[['MASR_Mean', 'Gender01', 'ZFirst_Score', 'Working_Memory_Score', 'Group']])
    # bootstrap indirect
    nboot, rng = 5000, np.random.default_rng(42)
    idx = np.arange(len(dat))
    ab = []
    for _ in range(nboot):
        bs = dat.iloc[rng.choice(idx, size=len(idx), replace=True)]
        res_a = fit_ols_robust(bs['MASR_Mean'],
                            bs[['Gender01','ZFirst_Score','Working_Memory_Score','Group']])
        a_bs,_,_,_ = coef_by_name(res_a, 'Gender01')

        res_b = fit_ols_robust(bs['ZSecond_Score'],
                            bs[['MASR_Mean','Gender01','ZFirst_Score','Working_Memory_Score','Group']])
        b_bs,_,_,_ = coef_by_name(res_b, 'MASR_Mean')
        ab.append(a_bs * b_bs)
    ab = np.array(ab)
    ci_lo, ci_hi = np.nanpercentile(ab, [2.5, 97.5])
    # Figure 3 (schematic)
    set_apa_style()
    fig, ax = plt.subplots(figsize=(6.5, 3.2))
    ax.axis('off')
    # nodes
    ax.text(0.10, 0.50, 'Gender', ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.3", fc='lightblue', ec='none'))
    ax.text(0.45, 0.80, 'Math Anxiety', ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.3", fc='lightgreen', ec='none'))
    ax.text(0.85, 0.50, 'Post-test (Z2)', ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.3", fc='lightcoral', ec='none'))
    # arrows
    ax.annotate('', xy=(0.40, 0.76), xytext=(0.17, 0.54), arrowprops=dict(arrowstyle='->', lw=2))
    ax.annotate('', xy=(0.80, 0.54), xytext=(0.50, 0.76), arrowprops=dict(arrowstyle='->', lw=2))
    ax.annotate('', xy=(0.80, 0.46), xytext=(0.17, 0.46), arrowprops=dict(arrowstyle='->', lw=1.5, color='gray'))
    # labels (standardized)
    a_beta = a_z.params.get('Gender01', np.nan)
    b_beta = b_z.params.get('MASR_Mean', np.nan)
    cprime = b_z.params.get('Gender01', np.nan)
    ax.text(0.28, 0.66, f"a = {a_beta:.2f}", ha='center')
    ax.text(0.66, 0.66, f"b = {b_beta:.2f}", ha='center')
    ax.text(0.49, 0.40, f"c' = {cprime:.2f}\nindirect a×b = {np.nanmean(ab):.2f}\n95% CI [{ci_lo:.2f}, {ci_hi:.2f}]",
            ha='center')
    ax.set_title('Mediation: Gender → Math Anxiety → Post-test (Z2)')
    fig.tight_layout()
    fig.savefig(os.path.join(FIG_DIR, 'fig3_mediation.pdf'))
    fig.savefig(os.path.join(FIG_DIR, 'fig3_mediation.png'))
    plt.close(fig)
    return {
        'a_beta_std': a_beta,
        'b_beta_std': b_beta,
        'cprime_beta_std': cprime,
        'indirect_mean': float(np.nanmean(ab)),
        'indirect_ci_lo': float(ci_lo),
        'indirect_ci_hi': float(ci_hi),
        'a_table': a.summary().tables[1].as_html(),  # for archival if needed
        'b_table': b.summary().tables[1].as_html()
    }


# ----------------------------
# Orchestrate & save tables
# ----------------------------
def main():
    df, prefix = load_data()
    liwc_cols = shortlist_liwc(df, prefix)
    # RQ1/RQ2
    r_s, p_s, ancova_tab = analyze_rq1_rq2(df)
    # RQ3
    class_df, class_top = analyze_rq3_class_profile(df, prefix, liwc_cols)
    # RQ4
    corr_tab = analyze_rq4_correlations(df, prefix, liwc_cols)
    # RQ5
    med = analyze_rq5_mediation(df)

    # Save tables (compact, no excessive formatting)
    with pd.ExcelWriter(OUT_XLSX) as xw:
        ancova_tab.round(3).to_excel(xw, sheet_name='ANCOVA_RQ1_RQ2')
        if not (class_df is None or class_df.empty):
            class_df.round(3).to_excel(xw, sheet_name='ClassProfile_RQ3', index=False)
            class_top.round(3).to_excel(xw, sheet_name='ClassProfile_Top', index=False)
        if corr_tab is not None and not corr_tab.empty:
            corr_tab.round(3).to_excel(xw, sheet_name='Correlations_RQ4', index=False)
        if med:
            pd.DataFrame([{
                'a_beta_std': med['a_beta_std'],
                'b_beta_std': med['b_beta_std'],
                'cprime_beta_std': med['cprime_beta_std'],
                'indirect_mean': med['indirect_mean'],
                'indirect_ci_lo': med['indirect_ci_lo'],
                'indirect_ci_hi': med['indirect_ci_hi']
            }]).round(3).to_excel(xw, sheet_name='Mediation_RQ5', index=False)

    # Minimal console output
    print(f"N = {len(df)} | LIWC prefix = {prefix} | shortlist = {len(liwc_cols)}")
    print(f"RQ1 Spearman(MASR,Z2) r = {r_s:.3f}, p = {p_s:.3g}")
    print(f"Saved tables -> {OUT_XLSX}")
    print(f"Saved figures -> {FIG_DIR}/fig1_masr_vs_z2.*, fig2_class_profile.*, fig3_mediation.*")


if __name__ == '__main__':
    set_apa_style()
    main()
