#!/usr/bin/env python3
"""
K-12 Math Anxiety Study - LIWC Analysis by Gender and Class Type
Analyzing writing patterns across different demographic groups
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from statsmodels.stats.multitest import multipletests
from wordcloud import WordCloud
import warnings
warnings.filterwarnings('ignore')

# APA-style plotting parameters
plt.style.use('seaborn-v0_8-whitegrid')

# Define APA-compatible color palette
APA_COLORS = {
    'female': '#E74C3C',       # Red
    'male': '#3498DB',         # Blue
    'fast_track': '#F39C12',   # Orange
    'regular': '#27AE60',      # Green
    'control': '#95A5A6',      # Gray
    'treatment': '#8E44AD'     # Purple
}

plt.rcParams.update({
    'figure.figsize': (10, 8),
    'font.size': 10,
    'axes.labelsize': 10,
    'axes.titlesize': 11,
    'font.family': 'sans-serif',
    'axes.spines.top': False,
    'axes.spines.right': False
})

# ===== 1. DATA LOADING AND PREPARATION =====

def load_and_prepare_data(base_path="/Users/<USER>/math_anxiety/"):
    """Load data with gender and class type variables"""
    
    print("Loading data...")
    
    # Load main dataset
    df = pd.read_csv(base_path + "QLS_SPSS_Aug8.csv")
    
    # Load LIWC results
    liwc1 = pd.read_csv(base_path + "LIWC1_LIWC2015_results.csv")
    liwc2 = pd.read_csv(base_path + "LIWC2_LIWC2015_results.csv")
    
    # Rename LIWC columns
    liwc1_cols = {col: f'liwc1_{col}' if col != 'ID' else col for col in liwc1.columns}
    liwc2_cols = {col: f'liwc2_{col}' if col != 'ID' else col for col in liwc2.columns}
    
    liwc1.rename(columns=liwc1_cols, inplace=True)
    liwc2.rename(columns=liwc2_cols, inplace=True)
    
    # Merge datasets
    df = pd.merge(df, liwc1, on='ID', how='left')
    df = pd.merge(df, liwc2, on='ID', how='left')
    
    # Create readable labels
    df['gender_label'] = df['Gender'].map({1: 'Female', 2: 'Male'})
    df['class_type_label'] = df['FastRegularClass'].map({1: 'Fast-track', 2: 'Regular'})
    
    print(f"Total sample size: {len(df)}")
    print(f"Gender distribution: {df['gender_label'].value_counts().to_dict()}")
    print(f"Class type distribution: {df['class_type_label'].value_counts().to_dict()}")
    
    return df

# ===== 2. LIWC ANALYSIS BY GENDER =====

def analyze_liwc_by_gender(df):
    """Analyze LIWC features by gender"""
    
    print("\n=== LIWC ANALYSIS BY GENDER ===")
    
    # Select key LIWC variables
    liwc_vars = ['WC', 'Analytic', 'Clout', 'Authentic', 'Tone',
                 'posemo', 'negemo', 'anx', 'anger', 'sad',
                 'cogproc', 'insight', 'cause', 'discrep', 'tentat', 'certain',
                 'i', 'we', 'you', 'achieve', 'power', 'affiliation',
                 'focuspast', 'focuspresent', 'focusfuture']
    
    results = []
    
    for time in ['liwc1', 'liwc2']:
        print(f"\n{time.upper()} Results:")
        
        time_results = []
        
        for var in liwc_vars:
            col_name = f'{time}_{var}'
            if col_name in df.columns:
                female_data = df[df['Gender'] == 1][col_name].dropna()
                male_data = df[df['Gender'] == 2][col_name].dropna()
                
                if len(female_data) > 5 and len(male_data) > 5:
                    # Statistical test
                    stat, p_value = stats.mannwhitneyu(female_data, male_data)
                    
                    # Effect size (rank-biserial correlation)
                    n1, n2 = len(female_data), len(male_data)
                    r = 1 - (2*stat) / (n1*n2)
                    
                    # Cohen's d for interpretation
                    pooled_std = np.sqrt(((n1-1)*female_data.std()**2 + (n2-1)*male_data.std()**2) / (n1+n2-2))
                    d = (female_data.mean() - male_data.mean()) / pooled_std if pooled_std > 0 else 0
                    
                    time_results.append({
                        'Feature': var,
                        'Female_M': female_data.mean(),
                        'Female_SD': female_data.std(),
                        'Male_M': male_data.mean(),
                        'Male_SD': male_data.std(),
                        'p_value': p_value,
                        'Effect_size_r': r,
                        'Cohen_d': d
                    })
        
        # Apply FDR correction
        if time_results:
            p_values = [r['p_value'] for r in time_results]
            _, p_adjusted, _, _ = multipletests(p_values, alpha=0.05, method='fdr_bh')
            
            for i, result in enumerate(time_results):
                result['p_adjusted'] = p_adjusted[i]
                result['sig'] = '***' if p_adjusted[i] < 0.001 else '**' if p_adjusted[i] < 0.01 else '*' if p_adjusted[i] < 0.05 else ''
            
            # Sort by effect size
            time_results.sort(key=lambda x: abs(x['Cohen_d']), reverse=True)
            
            # Display significant results
            sig_results = [r for r in time_results if r['p_adjusted'] < 0.05]
            if sig_results:
                print(f"\nSignificant differences (FDR corrected):")
                for r in sig_results[:10]:  # Top 10
                    print(f"{r['Feature']:15s}: Female={r['Female_M']:6.2f}, Male={r['Male_M']:6.2f}, "
                          f"d={r['Cohen_d']:5.2f}, p={r['p_adjusted']:.3f}{r['sig']}")
            else:
                print("No significant differences after FDR correction")
        
        results.append((time, pd.DataFrame(time_results)))
    
    return results

# ===== 3. LIWC ANALYSIS BY CLASS TYPE =====

def analyze_liwc_by_class_type(df):
    """Analyze LIWC features by class type"""
    
    print("\n=== LIWC ANALYSIS BY CLASS TYPE ===")
    
    # Treatment group only
    df_treat = df[df['Group'] == 1].copy()
    
    liwc_vars = ['WC', 'Analytic', 'Clout', 'Authentic', 'Tone',
                 'posemo', 'negemo', 'anx', 'anger', 'sad',
                 'cogproc', 'insight', 'cause', 'discrep', 'tentat', 'certain',
                 'i', 'we', 'you', 'achieve', 'power', 'affiliation',
                 'focuspast', 'focuspresent', 'focusfuture']
    
    results = []
    
    for time in ['liwc1', 'liwc2']:
        print(f"\n{time.upper()} Results (Treatment Group):")
        
        time_results = []
        
        for var in liwc_vars:
            col_name = f'{time}_{var}'
            if col_name in df_treat.columns:
                fast_data = df_treat[df_treat['FastRegularClass'] == 1][col_name].dropna()
                regular_data = df_treat[df_treat['FastRegularClass'] == 2][col_name].dropna()
                
                if len(fast_data) > 5 and len(regular_data) > 5:
                    # Statistical test
                    stat, p_value = stats.mannwhitneyu(fast_data, regular_data)
                    
                    # Effect size
                    n1, n2 = len(fast_data), len(regular_data)
                    r = 1 - (2*stat) / (n1*n2)
                    
                    # Cohen's d
                    pooled_std = np.sqrt(((n1-1)*fast_data.std()**2 + (n2-1)*regular_data.std()**2) / (n1+n2-2))
                    d = (fast_data.mean() - regular_data.mean()) / pooled_std if pooled_std > 0 else 0
                    
                    time_results.append({
                        'Feature': var,
                        'Fast_track_M': fast_data.mean(),
                        'Fast_track_SD': fast_data.std(),
                        'Regular_M': regular_data.mean(),
                        'Regular_SD': regular_data.std(),
                        'p_value': p_value,
                        'Effect_size_r': r,
                        'Cohen_d': d
                    })
        
        # Apply FDR correction
        if time_results:
            p_values = [r['p_value'] for r in time_results]
            _, p_adjusted, _, _ = multipletests(p_values, alpha=0.05, method='fdr_bh')
            
            for i, result in enumerate(time_results):
                result['p_adjusted'] = p_adjusted[i]
                result['sig'] = '***' if p_adjusted[i] < 0.001 else '**' if p_adjusted[i] < 0.01 else '*' if p_adjusted[i] < 0.05 else ''
            
            # Sort by effect size
            time_results.sort(key=lambda x: abs(x['Cohen_d']), reverse=True)
            
            # Display significant results
            sig_results = [r for r in time_results if r['p_adjusted'] < 0.05]
            if sig_results:
                print(f"\nSignificant differences (FDR corrected):")
                for r in sig_results[:10]:
                    print(f"{r['Feature']:15s}: Fast={r['Fast_track_M']:6.2f}, Regular={r['Regular_M']:6.2f}, "
                          f"d={r['Cohen_d']:5.2f}, p={r['p_adjusted']:.3f}{r['sig']}")
        
        results.append((time, pd.DataFrame(time_results)))
    
    return results

# ===== 4. WORD CLOUD GENERATION =====

def create_word_clouds(df):
    """Create word clouds for different groups"""
    
    print("\n=== GENERATING WORD CLOUDS ===")
    
    # Treatment group only for meaningful writing
    df_treat = df[df['Group'] == 1].copy()
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Gender comparison
    for idx, (gender, label) in enumerate([(1, 'Female'), (2, 'Male')]):
        ax = axes[0, idx]
        
        # Combine all writing samples
        texts = df_treat[df_treat['Gender'] == gender]['@1_English_Writing'].dropna()
        if len(texts) > 0:
            combined_text = ' '.join(texts.astype(str))
            
            # Generate word cloud
            wordcloud = WordCloud(width=800, height=400, 
                                background_color='white',
                                colormap='viridis',
                                max_words=100).generate(combined_text)
            
            ax.imshow(wordcloud, interpolation='bilinear')
            ax.set_title(f'{label} Students - Writing Patterns', fontsize=14, fontweight='bold')
            ax.axis('off')
    
    # Class type comparison
    for idx, (class_type, label) in enumerate([(1, 'Fast-track'), (2, 'Regular')]):
        ax = axes[1, idx]
        
        texts = df_treat[df_treat['FastRegularClass'] == class_type]['@1_English_Writing'].dropna()
        if len(texts) > 0:
            combined_text = ' '.join(texts.astype(str))
            
            wordcloud = WordCloud(width=800, height=400, 
                                background_color='white',
                                colormap='plasma',
                                max_words=100).generate(combined_text)
            
            ax.imshow(wordcloud, interpolation='bilinear')
            ax.set_title(f'{label} Classes - Writing Patterns', fontsize=14, fontweight='bold')
            ax.axis('off')
    
    plt.suptitle('Word Cloud Analysis by Demographics', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('word_clouds_demographics.png', dpi=300, bbox_inches='tight')
    plt.show()

# ===== 5. EMOTION INTENSITY ANALYSIS =====

def analyze_emotion_intensity(df):
    """Analyze emotional expression intensity by groups"""
    
    print("\n=== EMOTION INTENSITY ANALYSIS ===")
    
    # Create emotion intensity score
    emotion_vars = ['liwc1_posemo', 'liwc1_negemo', 'liwc1_anx', 'liwc1_anger', 'liwc1_sad']
    
    # Calculate total emotion score
    df['emotion_intensity'] = df[emotion_vars].sum(axis=1)
    df['emotion_balance'] = (df['liwc1_posemo'] - df[['liwc1_negemo', 'liwc1_anx', 'liwc1_anger', 'liwc1_sad']].sum(axis=1))
    
    # Treatment group only
    df_treat = df[df['Group'] == 1].copy()
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. Emotion intensity by gender
    ax = axes[0, 0]
    gender_data = []
    for gender in [1, 2]:
        data = df_treat[df_treat['Gender'] == gender]['emotion_intensity'].dropna()
        if len(data) > 0:
            gender_data.append(data)
    
    if gender_data:
        parts = ax.violinplot(gender_data, positions=[1, 2], showmeans=True)
        for pc, color in zip(parts['bodies'], [APA_COLORS['female'], APA_COLORS['male']]):
            pc.set_facecolor(color)
            pc.set_alpha(0.7)
        
        ax.set_xticks([1, 2])
        ax.set_xticklabels(['Female', 'Male'])
        ax.set_ylabel('Total Emotion Words (%)')
        ax.set_title('Emotion Intensity by Gender', fontweight='bold')
    
    # 2. Emotion balance by gender
    ax = axes[0, 1]
    balance_data = []
    for gender in [1, 2]:
        data = df_treat[df_treat['Gender'] == gender]['emotion_balance'].dropna()
        if len(data) > 0:
            balance_data.append(data)
    
    if balance_data:
        parts = ax.violinplot(balance_data, positions=[1, 2], showmeans=True)
        for pc, color in zip(parts['bodies'], [APA_COLORS['female'], APA_COLORS['male']]):
            pc.set_facecolor(color)
            pc.set_alpha(0.7)
        
        ax.set_xticks([1, 2])
        ax.set_xticklabels(['Female', 'Male'])
        ax.set_ylabel('Positive - Negative Emotion')
        ax.set_title('Emotion Balance by Gender', fontweight='bold')
        ax.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    
    # 3. Emotion intensity by class type
    ax = axes[1, 0]
    class_data = []
    for class_type in [1, 2]:
        data = df_treat[df_treat['FastRegularClass'] == class_type]['emotion_intensity'].dropna()
        if len(data) > 0:
            class_data.append(data)
    
    if class_data:
        parts = ax.violinplot(class_data, positions=[1, 2], showmeans=True)
        for pc, color in zip(parts['bodies'], [APA_COLORS['fast_track'], APA_COLORS['regular']]):
            pc.set_facecolor(color)
            pc.set_alpha(0.7)
        
        ax.set_xticks([1, 2])
        ax.set_xticklabels(['Fast-track', 'Regular'])
        ax.set_ylabel('Total Emotion Words (%)')
        ax.set_title('Emotion Intensity by Class Type', fontweight='bold')
    
    # 4. Emotion balance by class type
    ax = axes[1, 1]
    balance_class = []
    for class_type in [1, 2]:
        data = df_treat[df_treat['FastRegularClass'] == class_type]['emotion_balance'].dropna()
        if len(data) > 0:
            balance_class.append(data)
    
    if balance_class:
        parts = ax.violinplot(balance_class, positions=[1, 2], showmeans=True)
        for pc, color in zip(parts['bodies'], [APA_COLORS['fast_track'], APA_COLORS['regular']]):
            pc.set_facecolor(color)
            pc.set_alpha(0.7)
        
        ax.set_xticks([1, 2])
        ax.set_xticklabels(['Fast-track', 'Regular'])
        ax.set_ylabel('Positive - Negative Emotion')
        ax.set_title('Emotion Balance by Class Type', fontweight='bold')
        ax.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    plt.savefig('emotion_intensity_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print statistics
    print("\nEmotion Intensity Statistics:")
    print("\nBy Gender:")
    for gender, label in [(1, 'Female'), (2, 'Male')]:
        data = df_treat[df_treat['Gender'] == gender]['emotion_intensity'].dropna()
        if len(data) > 0:
            print(f"{label}: M={data.mean():.2f}, SD={data.std():.2f}, n={len(data)}")
    
    print("\nBy Class Type:")
    for class_type, label in [(1, 'Fast-track'), (2, 'Regular')]:
        data = df_treat[df_treat['FastRegularClass'] == class_type]['emotion_intensity'].dropna()
        if len(data) > 0:
            print(f"{label}: M={data.mean():.2f}, SD={data.std():.2f}, n={len(data)}")

# ===== 6. KEY LIWC INDICATORS HEATMAP =====

def create_liwc_heatmap(df):
    """Create heatmap of key LIWC indicators by groups"""
    
    print("\n=== CREATING LIWC HEATMAP ===")
    
    # Select key indicators
    key_indicators = ['liwc1_WC', 'liwc1_Analytic', 'liwc1_Clout', 'liwc1_Authentic', 'liwc1_Tone',
                     'liwc1_posemo', 'liwc1_negemo', 'liwc1_anx', 
                     'liwc1_cogproc', 'liwc1_insight', 'liwc1_cause',
                     'liwc1_i', 'liwc1_we', 'liwc1_achieve', 'liwc1_focusfuture']
    
    # Treatment group only
    df_treat = df[df['Group'] == 1].copy()
    
    # Calculate means for each group
    groups = {
        'Female': df_treat[df_treat['Gender'] == 1],
        'Male': df_treat[df_treat['Gender'] == 2],
        'Fast-track': df_treat[df_treat['FastRegularClass'] == 1],
        'Regular': df_treat[df_treat['FastRegularClass'] == 2]
    }
    
    heatmap_data = pd.DataFrame()
    
    for group_name, group_df in groups.items():
        means = []
        for indicator in key_indicators:
            if indicator in group_df.columns:
                mean_val = group_df[indicator].mean()
                means.append(mean_val)
            else:
                means.append(np.nan)
        
        heatmap_data[group_name] = means
    
    heatmap_data.index = [ind.replace('liwc1_', '') for ind in key_indicators]
    
    # Normalize by row (z-score)
    heatmap_z = heatmap_data.subtract(heatmap_data.mean(axis=1), axis=0).divide(heatmap_data.std(axis=1), axis=0)
    
    # Create heatmap
    plt.figure(figsize=(8, 10))
    sns.heatmap(heatmap_z, cmap='RdBu_r', center=0,
                annot=True, fmt='.2f',
                cbar_kws={'label': 'Z-score (relative to row mean)'},
                linewidths=0.5,
                square=True)
    
    plt.title('LIWC Indicators by Demographic Groups\n(Standardized within each indicator)', 
              fontweight='bold', pad=20)
    plt.xlabel('Group')
    plt.ylabel('LIWC Indicator')
    
    plt.tight_layout()
    plt.savefig('liwc_indicators_heatmap.png', dpi=300, bbox_inches='tight')
    plt.show()

# ===== 7. INTERACTION ANALYSIS =====

def analyze_interactions(df):
    """Analyze gender × class type interactions"""
    
    print("\n=== INTERACTION ANALYSIS ===")
    
    # Treatment group only
    df_treat = df[df['Group'] == 1].copy()
    
    # Create interaction groups
    df_treat['interaction_group'] = df_treat['Gender'].astype(str) + '_' + df_treat['FastRegularClass'].astype(str)
    
    # Key variables to analyze
    key_vars = ['emotion_intensity', 'liwc1_anx', 'liwc1_cogproc', 'liwc1_i']
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.flatten()
    
    for idx, var in enumerate(key_vars):
        if idx < len(axes) and var in df_treat.columns:
            ax = axes[idx]
            
            # Prepare data
            plot_data = []
            labels = []
            colors = []
            
            for gender in [1, 2]:
                for class_type in [1, 2]:
                    data = df_treat[(df_treat['Gender'] == gender) & 
                                  (df_treat['FastRegularClass'] == class_type)][var].dropna()
                    
                    if len(data) > 0:
                        plot_data.append(data)
                        gender_label = 'F' if gender == 1 else 'M'
                        class_label = 'Fast' if class_type == 1 else 'Reg'
                        labels.append(f'{gender_label}-{class_label}\n(n={len(data)})')
                        
                        # Color scheme
                        if gender == 1 and class_type == 1:
                            colors.append('#E74C3C')  # Female Fast-track
                        elif gender == 1 and class_type == 2:
                            colors.append('#EC7063')  # Female Regular
                        elif gender == 2 and class_type == 1:
                            colors.append('#3498DB')  # Male Fast-track
                        else:
                            colors.append('#5DADE2')  # Male Regular
            
            # Create boxplot
            bp = ax.boxplot(plot_data, labels=labels, patch_artist=True)
            for patch, color in zip(bp['boxes'], colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            ax.set_ylabel(var.replace('_', ' ').title())
            ax.set_title(var.replace('_', ' ').title(), fontweight='bold')
            ax.grid(True, alpha=0.3)
    
    plt.suptitle('Gender × Class Type Interactions', fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig('gender_class_interactions.png', dpi=300, bbox_inches='tight')
    plt.show()

# ===== MAIN EXECUTION =====

def main():
    """Main execution function"""
    
    # Load data
    df = load_and_prepare_data()
    
    # Run analyses
    gender_results = analyze_liwc_by_gender(df)
    class_results = analyze_liwc_by_class_type(df)
    
    # Create visualizations
    create_word_clouds(df)
    analyze_emotion_intensity(df)
    create_liwc_heatmap(df)
    analyze_interactions(df)
    
    # Save results
    with pd.ExcelWriter('liwc_demographic_analysis.xlsx') as writer:
        # Gender results
        for time, result_df in gender_results:
            if not result_df.empty:
                result_df.to_excel(writer, sheet_name=f'Gender_{time}', index=False)
        
        # Class type results
        for time, result_df in class_results:
            if not result_df.empty:
                result_df.to_excel(writer, sheet_name=f'ClassType_{time}', index=False)
    
    print("\n✓ Results saved to 'liwc_demographic_analysis.xlsx'")
    print("✓ Visualizations saved as PNG files")
    
    return df, gender_results, class_results

if __name__ == "__main__":
    df, gender_results, class_results = main()